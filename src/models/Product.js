const mongoose = require('mongoose');
const { productStatusValue } = require('../constants/dbEnums');

const productSchema = new mongoose.Schema({
  name: JSON,
  description: JSON,
  price: {
    type: Number,
    required: true,
    min: 0
  },
  vendorPrice: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  discountPrice: {
    type: Number,
    min: 0,
    validate: {
      validator: function (v) {
        return !v || v < this.price;
      },
      message: 'Discount price must be less than regular price'
    }
  },
  images: [{
    type: String,
    required: true,
    trim: true
  }],
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  subCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubCategory',
    required: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: false
  },
  stock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  lowStockThreshold: {
    type: Number,
    min: 0,
    default: 50
  },
  stockUpdateStatus: {
    type: String,
    enum: [ 'NONE', 'PENDING' ],
    default: 'NONE'
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  // weight: {
  //   type: Number,
  //   required: true,
  //   min: 0
  // },
  // dimensions: {
  //   length: {
  //     type: Number,
  //     required: true,
  //     min: 0
  //   },
  //   width: {
  //     type: Number,
  //     required: true,
  //     min: 0
  //   },
  //   height: {
  //     type: Number,
  //     required: true,
  //     min: 0
  //   }
  // },
  featured: {
    type: Boolean,
    default: false
  },
  showOnHomepage: {
    type: Boolean,
    default: false,
    index: true
  },
  status: {
    type: String,
    enum: Object.values(productStatusValue),
    default: productStatusValue.PENDING
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'createdByModel',
    required: true
  },
  createdByModel: {
    type: String,
    required: true,
    enum: [ 'Admin', 'Vendor' ]
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'updatedByModel'
  },
  updatedByModel: {
    type: String,
    enum: [ 'Admin', 'Vendor' ]
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  approvedAt: {
    type: Date
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  // Variant configuration
  hasVariants: {
    type: Boolean,
    default: false
  },
  variantAttributes: {
    type: [ String ],
    default: []
  },
  deletedAt: {
    type: Date,
    default: null
  },
}, { timestamps: true });

// Add virtual field for variants
productSchema.virtual('variants', {
  ref: 'ProductVariant',
  localField: '_id',
  foreignField: 'product'
});

// Configure toJSON and toObject to include virtuals
productSchema.set('toJSON', { virtuals: true });
productSchema.set('toObject', { virtuals: true });

// Add indexes for better query performance
productSchema.index({ 'name.en': 'text', description: 'text' });
productSchema.index({ 'name.hi': 'text', description: 'text' });
productSchema.index({ category: 1 });
productSchema.index({ subCategory: 1 });
productSchema.index({ temple: 1 });
productSchema.index({ status: 1 });
productSchema.index({ featured: 1 });
productSchema.index({ createdBy: 1 });

const Product = mongoose.model('Product', productSchema);

module.exports = Product;
