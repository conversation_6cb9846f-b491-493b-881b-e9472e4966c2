/**
 * File to represent the api response messages
 */
const messages = {
  // errors
  SERVER_ERROR: 'Something went wrong',
  ACCESS_DENIED: 'Access denied',
  NOT_FOUND: 'Not found',
  INVALID_OTP: 'Please enter the correct OTP & try again',
  OTP_EXPIRED: 'OTP expired',
  USER_NOT_FOUND: 'User not found',
  USER_INACTIVE: 'Your account has been deactivated. Please contact the admin for further details',
  ALREADY_EXISTS: 'Already Exists',
  ERROR: 'API executed with errors',
  NOT_AUTHORIZED: 'Your session has expired. Please login again',
  INVALID_TOKEN: 'Invalid token',
  INVALID_MONGO_ID: 'Invalid ID format',
  FCM_TOKEN_REQUIRED: 'FCM token is required',

  //success
  SUCCESS: 'API executed successfully',
  SUCCESS_MESSAGE: 'Success',
  OTP_SENT: 'An OTP has been sent on this phone number',

  //users
  USER_ALREADY_EXISTS: 'User already exists!',
  USER_ALREADY_REGISTERED: 'This user is already registered on <PERSON>ur<PERSON><PERSON><PERSON>',
  VERIFY_EMAIL_ACCOUNT: 'Please verify your email account',
  EMAIL_ACCOUNT_VERIFIED: 'Email verified successfully',
  PHONE_NUMBER_VERIFIED: 'Phone number verified successfully',
  VERIFY_PHONE_NUMBER: 'Please verify your phone number first',
  USER_REGISTERED_SUCCESSFULLY: 'User registered successfully',
  VERIFY_OLD_EMAIL_FIRST: 'Please verify your old email first to complete this action',
  NEW_EMAIL_CANNOT_BE_SAME: 'New email can\'t be the same as old email',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  PHONE_NUMBER_ALREADY_EXISTS: 'Phone number already exists',
  ADDRESS_NOT_FOUND: 'Address not found',
  TEMPLE_ADMIN_NOT_FOUND: 'Temple admin not found',
  LIVE_DARSHAN_SCHEDULED: 'Live Darshan has already been started',
  INVALID_USER_ID: 'Invalid user ID',
  NOTIFICATION_NOT_FOUND: 'Notification not found',

  // koisk
  KIOSK_ALREADY_EXISTS: 'Kiosk already exists for this temple',

  //validation

  CONFIRM_PASSWORD_NOT_MATCHED: 'Confirm Password doesn\'t match',
  PASSWORD_NOT_VALID: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  CURRENT_PASSWORD_NOT_VALID: 'Current Password must contain at least one uppercase letter, one lowercase letter, and one number',
  INVALID_CREDENTIALS: 'Invalid credentials',
  LOGOUT_ERROR: 'User logout error',
  LOGOUT_SUCCESS: 'User logout successfully',

  // favorites - temples
  TEMPLE_ADDED_TO_FAVORITES: 'Temple added to favorites successfully',
  TEMPLE_REMOVED_FROM_FAVORITES: 'Temple removed from favorites successfully',
  TEMPLE_NOT_IN_FAVORITES: 'Temple is not in favorites',
  TEMPLE_ALREADY_IN_FAVORITES: 'Temple is already in favorites',
  TEMPLE_NOT_FOUND: 'Temple not found',

  // favorites - products
  PRODUCT_ADDED_TO_FAVORITES: 'Product added to favorites successfully',
  PRODUCT_REMOVED_FROM_FAVORITES: 'Product removed from favorites successfully',
  PRODUCT_NOT_IN_FAVORITES: 'Product is not in favorites',
  PRODUCT_ALREADY_IN_FAVORITES: 'Product is already in favorites',
  VARIANT_ADDED_TO_FAVORITES: 'Product variant added to favorites successfully',
  VARIANT_REMOVED_FROM_FAVORITES: 'Product variant removed from favorites successfully',
  VARIANT_NOT_IN_FAVORITES: 'Product variant is not in favorites',
  VARIANT_ALREADY_IN_FAVORITES: 'Product variant is already in favorites',

  // live-darshan
  LIVE_DARSHAN_CREATED: 'Live Darshan created successfully',
  LIVE_DARSHAN_CREATION_FAILED: 'Live Darshan creation failed',
  LIVE_DARSHAN_NOT_FOUND: 'Live Darshan not found',
  LIVE_DARSHAN_UPDATED: 'Live Darshan updated successfully',
  LIVE_DARSHAN_DELETED: 'Live Darshan deleted successfully',
  RECORDING_NOT_FOUND: 'Recording not found',

  // booking
  DARSHAN_BOOKING_CREATED: 'Darshan booking created successfully',
  POOJA_BOOKING_CREATED: 'Puja booking created successfully',
  BOOKING_NOT_FOUND: 'Booking not found',

  // events
  EVENT_CREATED: 'Event created successfully',
  EVENT_UPDATED: 'Event updated successfully',
  EVENT_DELETED: 'Event deleted successfully',
  EVENT_NOT_FOUND: 'Event not found',
  EVENT_RETRIEVED: 'Event retrieved successfully',
  EVENTS_RETRIEVED: 'Events retrieved successfully',
  HOMEPAGE_EVENTS_RETRIEVED: 'Homepage events retrieved successfully',
  EVENT_IMAGE_DELETED: 'Event image deleted successfully',
  UPLOAD_URL_GENERATED: 'Upload URL generated successfully',
  EVENT_BOOKING_CREATED: 'Event booking created successfully',

  // verification
  DEVOTEE_ALREADY_VERIFIED: 'Devotee already checked in',
  DEVOTEE_VERIFIED_SUCCESS: 'Devotee successfully checked in',
  DEVOTEE_ALREADY_VERIFIED_DATE: 'Devotee already checked in for this date',
  DEVOTEE_VERIFIED_SUCCESS_DATE: 'Devotee successfully checked in for this date',
  DEVOTEE_NOT_FOUND: 'Devotee not found in this booking',
  INVALID_EVENT_DATE: 'Invalid date for this event booking',
  NO_EVENT_DATES: 'No dates available for this event booking',

  // temple category
  TEMPLE_CATEGORY_CREATED: 'Temple category created successfully',
  TEMPLE_CATEGORIES_RETRIEVED: 'Temple categories retrieved successfully',
  TEMPLE_CATEGORY_RETRIEVED: 'Temple category retrieved successfully',
  TEMPLE_CATEGORY_UPDATED: 'Temple category updated successfully',
  TEMPLE_CATEGORY_DELETED: 'Temple category deleted successfully',

  // inventory
  INVENTORY_SENT: 'Inventory sent successfully',
  INVENTORY_RECEIVED: 'Inventory received successfully',
  INVENTORIES_RETRIEVED: 'Inventories retrieved successfully',
  INVENTORY_RETRIEVED: 'Inventory retrieved successfully',
  INVENTORY_UPDATED: 'Inventory updated successfully',

  // vendor management
  VENDOR_CREATED: 'Vendor created successfully',
  VENDOR_UPDATED: 'Vendor updated successfully',
  VENDOR_RETRIEVED: 'Vendor retrieved successfully',
  VENDORS_RETRIEVED: 'Vendors retrieved successfully',
  VENDOR_DOCUMENT_DELETED: 'Vendor document deleted successfully',

  // shop - products
  PRODUCT_CREATED: 'Product created successfully',
  PRODUCT_CREATED_PENDING_APPROVAL: 'Product created successfully and pending approval',
  PRODUCT_UPDATED: 'Product updated successfully',
  PRODUCT_DELETED: 'Product deleted successfully',
  PRODUCT_NOT_FOUND: 'Product not found',
  PRODUCT_APPROVED: 'Product approved successfully',
  PRODUCT_REJECTED: 'Product rejected successfully',
  PRODUCT_FEATURED: 'Product featured successfully',
  PRODUCT_UNFEATURED: 'Product unfeatured successfully',
  PRODUCT_NOT_PENDING: 'Product is not in pending state',
  INVALID_PRODUCT_ID: 'Invalid product ID',
  SKU_ALREADY_EXISTS: 'SKU already exists',
  ONLY_ACTIVE_PRODUCTS_CAN_BE_FEATURED: 'Only active products can be featured',
  REJECTION_REASON_REQUIRED: 'Rejection reason is required',
  INSUFFICIENT_STOCK: 'Insufficient stock',
  REVIEW_ADDED_SUCCESSFULLY: 'Review added successfully',
  PRODUCTS_RETRIEVED: 'Products retrieved successfully',
  PRODUCT_RETRIEVED: 'Product retrieved successfully',
  UNAUTHORIZED_ACCESS: 'You are not authorized to access this resource',
  PRODUCT_REVIEW_RETRIEVED: 'Product review details retrieved successfully',
  PRODUCT_NOTIFICATION_SENT: 'Product notification sent successfully',

  // shop - categories
  CATEGORY_CREATED: 'Category created successfully',
  CATEGORY_UPDATED: 'Category updated successfully',
  CATEGORY_DELETED: 'Category deleted successfully',
  CATEGORY_NOT_FOUND: 'Category not found',
  CATEGORY_ALREADY_EXISTS: 'Category already exists',
  CATEGORY_NAME_ALREADY_EXISTS: 'Category name already exists',
  CATEGORY_HAS_CHILD_CATEGORIES: 'Category has child categories',
  CATEGORY_HAS_PRODUCTS: 'Category has products',
  INVALID_CATEGORY_ID: 'Invalid category ID',
  PARENT_CATEGORY_NOT_FOUND: 'Parent category not found',

  // shop - subcategories
  SUBCATEGORY_CREATED: 'Subcategory created successfully',
  SUBCATEGORY_UPDATED: 'Subcategory updated successfully',
  SUBCATEGORY_DELETED: 'Subcategory deleted successfully',
  SUBCATEGORY_NOT_FOUND: 'Subcategory not found',
  SUBCATEGORY_ALREADY_EXISTS: 'Subcategory already exists',
  SUBCATEGORY_NAME_ALREADY_EXISTS: 'Subcategory name already exists',
  SUBCATEGORY_HAS_PRODUCTS: 'Subcategory has products',
  INVALID_SUBCATEGORY_ID: 'Invalid subcategory ID',
  SUBCATEGORY_NOT_BELONG_TO_CATEGORY: 'Subcategory does not belong to the selected category',

  // shop - collections
  COLLECTION_CREATED: 'Collection created successfully',
  COLLECTION_UPDATED: 'Collection updated successfully',
  COLLECTION_DELETED: 'Collection deleted successfully',
  COLLECTION_NOT_FOUND: 'Collection not found',
  COLLECTION_ALREADY_EXISTS: 'Collection already exists',
  COLLECTION_NAME_ALREADY_EXISTS: 'Collection name already exists',
  INVALID_COLLECTION_ID: 'Invalid collection ID',

  // shop - cart
  ITEM_ADDED_TO_CART: 'Item added to cart successfully',
  ITEM_REMOVED_FROM_CART: 'Item removed from cart successfully',
  CART_UPDATED: 'Cart updated successfully',
  CART_CLEARED: 'Cart cleared successfully',
  CART_NOT_FOUND: 'Cart not found',
  ITEM_NOT_IN_CART: 'Item not in cart',
  CART_EMPTY: 'Cart is empty',

  // shop - orders
  ORDER_CREATED: 'Order created successfully',
  ORDER_UPDATED: 'Order updated successfully',
  ORDER_CANCELLED: 'Order cancelled successfully',
  ORDER_NOT_FOUND: 'Order not found',
  ORDER_ALREADY_CANCELLED: 'Order is already cancelled',
  ORDER_CANNOT_BE_CANCELLED: 'Order cannot be cancelled',
  ORDER_STATUS_UPDATED: 'Order status updated successfully',
  ORDER_REFUNDED: 'Order refunded successfully',
  ORDER_SHIPPED: 'Order shipped successfully',
  ORDER_CANNOT_BE_REFUNDED: 'Order cannot be refunded',
  ORDER_CANNOT_BE_SHIPPED: 'Order cannot be shipped',
  ORDER_NOT_PENDING: 'Order is not in pending state',
  INVALID_ORDER_ID: 'Invalid order ID',
  REFUND_AMOUNT_EXCEEDS_ORDER_TOTAL: 'Refund amount exceeds order total',

  // shipping
  SHIPROCKET_ORDER_CREATED: 'ShipRocket order created successfully',
  SHIPMENT_UPDATED: 'Shipment updated successfully',
  AWB_GENERATED: 'AWB generated successfully',
  PICKUP_REQUESTED: 'Pickup requested successfully',
  SHIPMENT_CANCELLED: 'Shipment cancelled successfully',

  // shop - payments
  PAYMENT_SUCCESSFUL: 'Payment successful',
  PAYMENT_FAILED: 'Payment failed',
  PAYMENT_ALREADY_PROCESSED: 'Payment already processed',
  PAYMENT_RECORD_NOT_FOUND: 'Payment record not found',
  RAZORPAY_ORDER_CREATED: 'Razorpay order created successfully',
  RAZORPAY_ORDER_NOT_CREATED: 'Razorpay order not created',
  INVALID_PAYMENT_SIGNATURE: 'Invalid payment signature',
  PAYMENT_GATEWAY_ERROR: 'Error communicating with payment gateway',
  INVALID_WEBHOOK_SIGNATURE: 'Invalid webhook signature',
  WEBHOOK_PROCESSED: 'Webhook processed successfully',

  // vendors
  VENDOR_REGISTERED: 'Vendor registered successfully',
  VENDOR_REGISTRATION_PENDING_APPROVAL: 'Vendor registration successful. Your account is pending approval.',
  VENDOR_APPROVED: 'Vendor approved successfully',
  VENDOR_REJECTED: 'Vendor rejected successfully',
  VENDOR_DELETED: 'Vendor deleted successfully',
  VENDOR_NOT_FOUND: 'Vendor not found',
  VENDOR_ACCOUNT_NOT_ACTIVE: 'Your vendor account is not active',
  VENDOR_HAS_PRODUCTS: 'Vendor has products',
  INVALID_VENDOR_ID: 'Invalid vendor ID',
  LOGIN_SUCCESS: 'Login successful',
  PASSWORD_RESET_EMAIL_SENT: 'Password reset email sent',
  PASSWORD_RESET_SUCCESS: 'Password reset successful',
  PASSWORD_CHANGED: 'Password changed successfully',
  CURRENT_PASSWORD_INCORRECT: 'Current password is incorrect',
  INVALID_OR_EXPIRED_TOKEN: 'Invalid or expired token',
  PROFILE_UPDATED: 'Profile updated successfully',
  INVALID_TEMPLE_ID: 'Invalid temple ID',

  // Product variants
  VARIANT_ATTRIBUTES_REQUIRED: 'Variant attributes are required for products with variants',
  VARIANTS_REQUIRED: 'At least one variant is required for products with variants',
  ATTRIBUTE_NOT_FOUND: 'Attribute not found',
  MISSING_VARIANT_ATTRIBUTE: 'Missing required variant attribute',
  INVALID_VARIANT_ATTRIBUTE: 'Invalid variant attribute',
  DEFAULT_VARIANT_REQUIRED: 'At least one variant must be marked as default',
  INVALID_VARIANT_ID: 'Invalid variant ID',
  VARIANT_NOT_FOUND: 'Variant not found',
  DEFAULT_VARIANT_NOT_FOUND: 'Default variant not found',
  CANNOT_REMOVE_ALL_VARIANTS: 'Cannot remove all variants from a product',
  CANNOT_REMOVE_ALL_DEFAULT_VARIANTS: 'Cannot remove all default variants',

  // Attributes
  ATTRIBUTE_CREATED: 'Attribute created successfully',
  ATTRIBUTE_UPDATED: 'Attribute updated successfully',
  ATTRIBUTE_DELETED: 'Attribute deleted successfully',
  ATTRIBUTE_ALREADY_EXISTS: 'Attribute already exists',
  ATTRIBUTE_NAME_ALREADY_EXISTS: 'Attribute name already exists',
  ATTRIBUTE_IN_USE: 'Attribute is in use by category/products',

  // Settlements
  SETTLEMENT_CREATED: 'Settlement batch created successfully',
  SETTLEMENT_UPDATED: 'Settlement batch updated successfully',
  SETTLEMENT_APPROVED: 'Settlement batch approved successfully',
  SETTLEMENT_PAID: 'Settlement batch marked as paid successfully',
  SETTLEMENT_REJECTED: 'Settlement batch rejected successfully',
  SETTLEMENT_NOT_FOUND: 'Settlement batch not found',
  NO_ORDERS_TO_SETTLE: 'No delivered orders found in the selected date range',
  SETTLEMENT_ALREADY_PAID: 'Settlement batch is already paid',
  SETTLEMENT_ALREADY_REJECTED: 'Settlement batch is already rejected',
  BANK_TRANSACTION_ID_REQUIRED: 'Bank transaction ID is required for paid settlements',

  // Discounts 
  DISCOUNT_CREATED: 'Discount created successfully',
  DISCOUNT_UPDATED: 'Discount updated successfully',
  DISCOUNTS_RETRIEVED: 'Discounts retrieved successfully',
  DISCOUNT_RETRIEVED: 'Discount retrieved successfully',
  DISCOUNT_DELETED: 'Discount deleted successfully',
  INVOICE_GENERATED: 'Commission invoice generated successfully',
  DISCOUNT_APPLIED: 'Discount applied successfully',

  // Shop Banners 
  BANNER_NOT_FOUND: 'Banner not found',

  // Offerings
  OFFERING_CREATED: 'Offering created successfully',
  OFFERINGS_RETRIEVED: 'Offerings retrieved successfully',
  OFFERING_RETRIEVED: 'Offering retrieved successfully',
  OFFERING_UPDATED: 'Offering updated successfully',
  OFFERING_DELETED: 'Offering deleted successfully',

  // Centralized Approval Center
  COUNT_RETRIEVED: 'Counts retrieved successfully',

  // Temple 
  TEMPLE_DELETED: 'Temple deleted successfully',

  // Helicopter
  HELICOPTER_CREATED: 'Helicopter created successfully',
  HELICOPTER_UPDATED: 'Helicopter updated successfully',
  HELICOPTER_DELETED: 'Helicopter deleted successfully',
  HELICOPTERS_RETRIEVED: 'Helicopters retrieved successfully',
  HELICOPTER_RETRIEVED: 'Helicopter retrieved successfully',

  // Helicopter Puja
  HELICOPTER_PUJA_CREATED: 'Helicopter puja created successfully',
  HELICOPTER_PUJAS_RETRIEVED: 'Helicopter pujas retrieved successfully',
  HELICOPTER_PUJA_RETRIEVED: 'Helicopter puja retrieved successfully',
  HELICOPTER_PUJA_UPDATED: 'Helicopter puja updated successfully',
  HELICOPTER_PUJA_DELETED: 'Helicopter puja deleted successfully',

  // Helicopter Booking
  HELICOPTER_BOOKING_CREATED: 'Helicopter booking created successfully',

  // Notifiction 
  NOTIFICATIONS_RETRIEVED: 'Notifications retrieved successfully',
  NOTIFICATION_MARKED_AS_READ: 'Notification marked as read successfully',
  ALL_NOTIFICATIONS_MARKED_AS_READ: 'All notifications marked as read successfully',

  // Panchang
  MUHURAT_RETRIEVED_SUCCESSFULLY: 'Muhurat retrieved successfully',
  DURMUHURAT_RETRIEVED_SUCCESSFULLY: 'Durmuhurat retrieved successfully',
  HORA_RETRIEVED_SUCCESSFULLY: 'Hora retrieved successfully'
};

module.exports = {
  messages,
};
