const bcrypt = require('bcryptjs');
const KioskUser = require('../../../models/KioskUser');
const Kiosk = require('../../../models/Kiosk');
const Temple = require('../../../models/Temple');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { messages } = require('../../../messages');
const { generatePassword } = require('../../../utils/passwordGenerator');
const { sendMail } = require('../../../utils/sendMail');
// const { sendWhatsAppMessage } = require('../../../utils/whatsappService');
const { emailSubjects } = require('../../../messages/emailSubjects');
const path = require('path');
const { transformTranslatedFields } = require('../../../utils/localizer');

/**
 * Create a new kiosk user
 * @param {Object} userData - Kiosk user data
 * @param {Object} adminUser - Admin user creating the kiosk user
 * @returns {Object} Created kiosk user
 */
const createKioskUser = async (userData, adminUser) => {
  // Check if kiosk exists
  const kiosk = await Kiosk.findById(userData.kiosk);

  if (!kiosk) {
    throwNotFoundError('Kiosk not found');
  }

  // Check if email already exists
  const existingUserWithEmail = await KioskUser.findOne({ email: userData.email.toLowerCase() });

  if (existingUserWithEmail) {
    throwBadRequestError(`Email ${userData.email} is already in use`);
  }

  // Check if WhatsApp number already exists
  const existingUserWithWhatsapp = await KioskUser.findOne({
    phoneNumber: userData.phoneNumber,
    countryCode: userData.countryCode || '+91'
  });

  if (existingUserWithWhatsapp) {
    throwBadRequestError(`WhatsApp number ${userData.phoneNumber} is already in use`);
  }

  // Get temple details for notification
  const temple = await Temple.findById(kiosk.temple);

  if (!temple) {
    throwNotFoundError(messages.TEMPLE_NOT_FOUND);
  }

  // Generate a random password
  const password = generatePassword();
  const hashedPassword = await bcrypt.hash(password, 10);

  // Create the kiosk user
  const kioskUser = new KioskUser({
    name: userData.name,
    email: userData.email.toLowerCase(),
    phoneNumber: userData.phoneNumber,
    countryCode: userData.countryCode || '+91',
    kiosk: userData.kiosk,
    password: hashedPassword,
    status: userData.status || 'ACTIVE',
    createdBy: adminUser.id,
    updatedBy: adminUser.id
  });

  await kioskUser.save();

  // Send notifications
  const email = userData.email.toLowerCase();
  const subject = emailSubjects.KIOSK_USER_ACCOUNT_CREDENTIALS;
  const templatePath = path.join(__dirname, '../../../views/welcomeKioskUser.html');
  const data = {
    name: kioskUser.name,
    templeName: temple.name,
    kioskLocation: kiosk.location,
    email,
    password: password,
    androidAppLink: process.env.ANDROID_APP_LINK,
    iosAppLink: process.env.IOS_APP_LINK,
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS
  };

  await sendMail(email, subject, templatePath, data);

  // Return user without password
  const userObject = kioskUser.toObject();

  // delete userObject.password;
  userObject.password = password;

  return {
    ...userObject,
    temple,
    kiosk
  };
};

/**
 * Get a kiosk user by ID
 * @param {string} id - Kiosk user ID
 * @returns {Object} Kiosk user
 */
const getKioskUserById = async (id) => {
  const kioskUser = await KioskUser.findById(id)
    .select('-password')
    .populate({
      path: 'kiosk',
      populate: {
        path: 'temple',
      }
    }).lean();

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(kioskUser, language.code);
};

/**
 * Update a kiosk user
 * @param {string} id - Kiosk user ID
 * @param {Object} updateData - Updated kiosk user data
 * @param {Object} adminUser - Admin user updating the kiosk user
 * @returns {Object} Updated kiosk user
 */
const updateKioskUser = async (id, updateData, adminUser) => {
  // Find the kiosk user
  const kioskUser = await KioskUser.findById(id);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Check if kiosk is being changed and if it exists
  if (updateData.kiosk && updateData.kiosk !== kioskUser.kiosk.toString()) {
    const kiosk = await Kiosk.findById(updateData.kiosk);

    if (!kiosk) {
      throwNotFoundError('Kiosk not found');
    }
    kioskUser.kiosk = updateData.kiosk;
  }

  // Check if email is being changed and if it's already in use
  if (updateData.email && updateData.email.toLowerCase() !== kioskUser.email) {
    const emailExists = await KioskUser.findOne({
      email: updateData.email.toLowerCase(),
      _id: { $ne: kioskUser._id }
    });

    if (emailExists) {
      throwBadRequestError(`Email ${updateData.email} is already in use`);
    }

    kioskUser.email = updateData.email.toLowerCase();
  }

  // Check if WhatsApp number is being changed and if it's already in use
  if ((updateData.phoneNumber && updateData.phoneNumber !== kioskUser.phoneNumber) ||
      (updateData.countryCode && updateData.countryCode !== kioskUser.countryCode)) {
    const whatsappExists = await KioskUser.findOne({
      phoneNumber: updateData.phoneNumber || kioskUser.phoneNumber,
      countryCode: updateData.countryCode || kioskUser.countryCode,
      _id: { $ne: kioskUser._id }
    });

    if (whatsappExists) {
      throwBadRequestError(`WhatsApp number ${updateData.phoneNumber || kioskUser.phoneNumber} is already in use`);
    }

    if (updateData.phoneNumber) {
      kioskUser.phoneNumber = updateData.phoneNumber;
    }

    if (updateData.countryCode) {
      kioskUser.countryCode = updateData.countryCode;
    }
  }

  // Update other fields
  if (updateData.name) {
    kioskUser.name = updateData.name;
  }

  if (updateData.status) {
    kioskUser.status = updateData.status;
  }

  // Reset password if requested
  if (updateData.resetPassword) {
    // Get kiosk and temple details for notification
    const kiosk = await Kiosk.findById(kioskUser.kiosk);
    const temple = await Temple.findById(kiosk.temple);

    // Generate a new password
    const password = generatePassword();
    const hashedPassword = await bcrypt.hash(password, 10);

    kioskUser.password = hashedPassword;
    kioskUser.passwordChangeRequired = true;

    // Send notifications with new password
    // await sendKioskUserNotifications(kioskUser, temple, kiosk, password);
  }

  kioskUser.updatedBy = adminUser.id;
  await kioskUser.save();

  // Return updated user
  return await getKioskUserById(id);
};

/**
 * Delete a kiosk user
 * @param {string} id - Kiosk user ID
 * @returns {Object} Deletion result
 */
const deleteKioskUser = async (id) => {
  const kioskUser = await KioskUser.findById(id);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  if (kioskUser.deletedAt || kioskUser.status === 'INACTIVE') {
    throwBadRequestError('Kiosk user already deleted or inactive');
  }

  //* Soft delete kiosk user 
  kioskUser.status = 'INACTIVE';
  kioskUser.deletedAt = new Date();
  await kioskUser.save();

  return kioskUser;
};

/**
 * List kiosk users with pagination and filtering
 * @param {Object} filters - Filter criteria
 * @returns {Object} Paginated list of kiosk users
 */
const listKioskUsers = async (filters) => {
  const { page = 1, limit = 10, kiosk, status, temple, search } = filters;
  const skip = (page - 1) * limit;

  // Build query
  const query = {};

  if (kiosk) {
    query.kiosk = kiosk;
  }

  if (status) {
    query.status = status;
  }

  // If temple is provided, find all kiosks for that temple first
  if (temple) {
    const kiosks = await Kiosk.find({ temple });
    const kioskIds = kiosks.map(k => k._id);

    query.kiosk = { $in: kioskIds };
  }

  // Add search functionality
  if (search) {
    const searchRegex = new RegExp(search, 'i');

    query.$or = [
      { name: searchRegex },
      { email: searchRegex },
      { phoneNumber: searchRegex }
    ];
  }

  // Get total count
  const total = await KioskUser.countDocuments(query);

  // Get kiosk users
  const kioskUsers = await KioskUser.find(query)
    .select('-password')
    .populate({
      path: 'kiosk',
      populate: {
        path: 'temple',
      }
    })
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit)).lean();

  const language = { code: 'en' };

  return {
    kioskUsers: await transformTranslatedFields(kioskUsers, language.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    }
  };
};

module.exports = {
  createKioskUser,
  getKioskUserById,
  updateKioskUser,
  deleteKioskUser,
  listKioskUsers
};
