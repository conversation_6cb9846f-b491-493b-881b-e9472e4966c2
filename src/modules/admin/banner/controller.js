const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { createBannerSchema, updateBannerSchema, uploadUrlSchema, validateId } = require('./validation');
const bannerService = require('./service');
const { getPresignedUrl } = require('../../../utils/s3Service');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

const createBanner = async (req, res) => {
  try {
    const { error } = createBannerSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const banner = await bannerService.createBanner(req.body, req.user.id);

    //* Save audit log 
    const detail = `Banner ${banner.name} created successfully`;
    const model = 'Banner';

    await saveAuditLog(req, req.user.id, auditLogAction.BANNER_CREATED, detail, model);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Banner created successfully',
      data: banner
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateBanner = async (req, res) => {
  try {
    const { error } = updateBannerSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const banner = await bannerService.updateBanner(
      req.params.id,
      req.body,
      req.user.id
    );

    //* Save audit log 
    const detail = `Banner ${banner.name} updated successfully`;
    const model = 'Banner';

    await saveAuditLog(req, req.user.id, auditLogAction.BANNER_UPDATED, detail, model);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Banner updated successfully',
      data: banner
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listBanners = async (req, res) => {
  try {
    const data = await bannerService.listBanners(req.query);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Banners retrieved successfully',
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteBanner = async (req, res) => {
  try {
    const banner = await bannerService.deleteBanner(req.params.id);

    //* Save audit log 
    const detail = `Banner ${banner.name} deleted successfully`;
    const model = 'Banner';

    await saveAuditLog(req, req.user.id, auditLogAction.BANNER_DELETED, detail, model);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Banner deleted successfully'
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'banners');

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Upload URL generated successfully',
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getBannerById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const banner = await bannerService.getBannerById(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Banner retrieved successfully',
      data: banner
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getUploadUrl,
  createBanner,
  updateBanner,
  listBanners,
  deleteBanner,
  getBannerById
};
