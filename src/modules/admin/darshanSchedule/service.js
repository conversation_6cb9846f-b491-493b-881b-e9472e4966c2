const DarshanSchedule = require('../../../models/DarshanSchedule');
const Temple = require('../../../models/Temple');
const { throwBadRequestError } = require('../../../errors');
const { default: mongoose } = require('mongoose');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');

const createDarshanSchedule = async (scheduleData, adminId) => {
  // Check if temple exists
  const temple = await Temple.findById(scheduleData.temple);

  if (!temple) {
    throwBadRequestError('Temple not found');
  }

  // Translate fields
  const translatedFields = ['name', 'description'];
  const translatedData = await translateDataForStore(translatedFields, scheduleData);

  translatedFields.forEach(field => {
    if (scheduleData[field]) {
      delete scheduleData[field];
    }
  });

  scheduleData = {
    ...scheduleData,
    ...translatedData
  };

  // Create darshan schedule
  const darshanSchedule = await DarshanSchedule.create({
    ...scheduleData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return darshanSchedule.populate([
    { path: 'temple', select: 'name location' },
    { path: 'createdBy', select: 'name email' },
    { path: 'updatedBy', select: 'name email' }
  ]);
};

const updateDarshanSchedule = async (scheduleId, updateData, adminId) => {
  const schedule = await DarshanSchedule.findById(scheduleId);
  
  if (!schedule) {
    throwBadRequestError('Darshan schedule not found');
  }

  // If temple is being updated, verify it exists
  if (updateData.temple) {
    const temple = await Temple.findById(updateData.temple);

    if (!temple) {
      throwBadRequestError('Temple not found');
    }
  }

  // Translate fields if provided
  const translatedFields = ['name', 'description'];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update the schedule
  const updatedSchedule = await DarshanSchedule.findByIdAndUpdate(
    scheduleId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  ).populate([
    { path: 'temple', select: 'name location' },
    { path: 'createdBy', select: 'name email' },
    { path: 'updatedBy', select: 'name email' }
  ]);

  return updatedSchedule;
};

const deleteDarshanSchedule = async (scheduleId) => {
  const schedule = await DarshanSchedule.findById(scheduleId);
  
  if (!schedule) {
    throwBadRequestError('Darshan schedule not found');
  }

  if (schedule.deletedAt || schedule.status === 'INACTIVE') {
    throwBadRequestError('Darshan schedule already deleted or inactive');
  }

  //* Soft delete darshan schedule 
  schedule.deletedAt = new Date();
  schedule.status = 'INACTIVE';
  await schedule.save();

  return schedule;
};

const listDarshanSchedules = async (query = {}) => {
  const { 
    page = 1, 
    limit = 10, 
    search,
    temple, 
    status,
    dateType,
    startDate,
    endDate,
    specificDate,
    sortBy = 'createdAt', 
    sortOrder = -1,
  } = query;

  const filter = {};

  if (temple) {
    filter.temple = new mongoose.Types.ObjectId(temple);
  }

  if (status) {
    filter.status = status;
  }

  if (dateType) {
    filter.dateType = dateType;
  }

  if (startDate) {
    filter['dateRange.startDate'] = { $gte: new Date(startDate) };
  }

  if (endDate) {
    filter['dateRange.endDate'] = { $lte: new Date(endDate) };
  }

  if (specificDate) {
    filter.specificDate = new Date(specificDate);
  }

  const aggregatePipeline = [{
    $lookup: {
      from: 'temples',
      localField: 'temple',
      foreignField: '_id',
      as: 'templeDetails'
    }
  },
  {
    $unwind: { path: '$templeDetails', preserveNullAndEmptyArrays: true }
  },
  {
    $lookup: {
      from: 'admins',
      localField: 'createdBy',
      foreignField: '_id',
      as: 'createdBy'
    }
  },
  {
    $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true }
  },
  {
    $lookup: {
      from: 'admins',
      localField: 'updatedBy',
      foreignField: '_id',
      as: 'updatedBy'
    }
  },
  {
    $unwind: {
      path: '$updatedBy',
      preserveNullAndEmptyArrays: true
    }
  }];

  const matchConditions = {
    $and: [
      { ...filter }

    ]
  };

  const language = { code: 'en' };

  if (search) {
    matchConditions.$and.push({ $or: [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`templeDetails.name.${language.code}`]: { $regex: search, $options: 'i' } },
    ] });
  }

  aggregatePipeline.push({ $match: matchConditions });

  const sortMongoOrder = parseInt(sortOrder) || -1; // Ensure sortOrder is a number
  
  if (sortBy === 'temple') {
    aggregatePipeline.push({
      $sort: {
        [`templeDetails.name.${language.code}`]: sortMongoOrder
      }
    });
  } else {
    aggregatePipeline.push({
      $sort: {
        [sortBy]: sortMongoOrder
      }
    });
  }

  aggregatePipeline.push({
    $facet: {
      data: [
        { $skip: (parseInt(page) - 1) * parseInt(limit) },
        { $limit: parseInt(limit) },
        {
          $project: {
            _id: 1,
            name: 1,
            description: 1,
            dateType: 1,
            duration: 1,
            specificDate: 1,
            dateRange: 1,
            timeSlots: 1,
            pricing: 1,
            occupancyPerSlot: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1,
            temple: {
              _id: '$templeDetails._id',
              name: '$templeDetails.name',
              location: '$templeDetails.location'
            },
            createdBy: {
              _id: '$createdBy._id',
              name: '$createdBy.name',
              email: '$createdBy.email'
            },
            updatedBy: {
              _id: '$updatedBy._id',
              name: '$updatedBy.name',
              email: '$updatedBy.email'
            }
          }
        }
      ],
      total: [{ $count: 'count' }]
    }
  });

  const [ result ] = await DarshanSchedule.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

  return {
    schedules: await transformTranslatedFields(result.data, language.code),
    pagination: {
      total: result.total[0]?.count || 0,
      page: parseInt(page),
      pages: Math.ceil((result.total[0]?.count || 0) / limit)
    }
  };
};

const getDarshanScheduleById = async (scheduleId) => {
  const schedule = await DarshanSchedule.findById(scheduleId)
    .populate([
      { path: 'temple', select: 'name location' },
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' }
    ]).lean();

  if (!schedule) {
    throwBadRequestError('Darshan schedule not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(schedule, language.code);
};

module.exports = {
  createDarshanSchedule,
  updateDarshanSchedule,
  deleteDarshanSchedule,
  listDarshanSchedules,
  getDarshanScheduleById
};
