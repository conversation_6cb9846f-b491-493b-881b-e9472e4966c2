const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const { getPresignedUrl } = require('../../../../utils/s3Service');
const categoryService = require('./service');
const { createCategorySchema, updateCategorySchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../../utils/auditLogger');
const { auditLogAction } = require('../../../../constants/dbEnums');

/**
 * Create a new category
 */
const createCategory = async (req, res) => {
  try {
    const { error } = createCategorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    
    const data = await categoryService.createCategory({
      adminId,
      ...req.body
    });

    //* Save audit log 
    const detail = `Category ${data.name} created successfully`;
    const model = 'Category';

    await saveAuditLog(req, req.user.id, auditLogAction.CATEGORY_CREATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.CATEGORY_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all categories
 */
const getAllCategories = async (req, res) => {
  try {
    const { includeInactive = false, limit = 10, page = 1, search, sortBy = 'createdAt', sortOrder = -1 } = req.query;
    
    const data = await categoryService.getAllCategories(includeInactive, limit, page, search, sortBy, sortOrder);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get category by ID
 */
const getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await categoryService.getCategoryById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update category
 */
const updateCategory = async (req, res) => {
  try {
    const { error } = updateCategorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;
    
    const data = await categoryService.updateCategory({
      adminId,
      categoryId: id,
      ...req.body
    });

    //* Save audit log 
    const detail = `Category ${data.name} updated successfully`;
    const model = 'Category';

    await saveAuditLog(req, req.user.id, auditLogAction.CATEGORY_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.CATEGORY_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete category
 */
const deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await categoryService.deleteCategory(id);

    //* Save audit log 
    const detail = `Category ${data.name} deleted successfully`;
    const model = 'Category';

    await saveAuditLog(req, req.user.id, auditLogAction.CATEGORY_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.CATEGORY_DELETED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get upload URL for category image
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { extension } = req.body;
    
    const uploadData = await getPresignedUrl(extension, 'categories');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getUploadUrl
};
