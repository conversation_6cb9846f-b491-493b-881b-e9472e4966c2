const ProductShipment = require('../../../../models/ProductShipment');
const Invoice = require('../../../../models/Invoice');
const Vendor = require('../../../../models/Vendor');
const Admin = require('../../../../models/Admin');
const { throwBadRequestError, throwNotFoundError } = require('../../../../errors');
const { shipmentStatusValue, settlementStatusValue } = require('../../../../constants/dbEnums');
const mongoose = require('mongoose');
const { generateCommissionInvoice } = require('../../../../utils/pdfGenerator');
const { messages } = require('../../../../messages');
const { transformTranslatedFields } = require('../../../../utils/localizer');
const moment = require('moment');

/**
 * Get all shipments with filters for settlement
 * @param {Object} params - Query parameters
 * @returns {Object} Shipments with pagination
 */
const getShipmentsForSettlement = async ({ page, limit, vendorId, startDate, endDate, status, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build the query
  const query = {
    status: shipmentStatusValue.DELIVERED
  };

  // Add filters if provided
  if (status) {
    if (status === 'PENDING') {
      query.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      query.settlementStatus = status;
    }
  }

  if (vendorId) {
    query.vendor = new mongoose.Types.ObjectId(vendorId);
    query.vendorModel = 'Vendor';
  } else {
    query.vendorModel = 'Vendor'; // Only show vendor shipments, not admin shipments
  }

  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  // Get shipments
  const shipments = await ProductShipment.find(query)
    .populate({
      path: 'vendor',
      select: 'businessName contactPersonName email phoneNumber'
    })
    .populate({
      path: 'order',
      select: 'orderNumber orderStatus paymentStatus createdAt'
    })
    .populate({
      path: 'settledBy',
      select: 'name email'
    })
    .populate({
      path: 'invoice',
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  // Get total count
  const total = await ProductShipment.countDocuments(query);

  const settlementSummary = await getSettlementSummary({ vendorId, startDate, endDate });

  const language = { code: 'en' };

  return {
    settlementSummary,
    shipments: await transformTranslatedFields(shipments, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get settlement summary for admin
 * @param {Object} filters - Filters for summary
 * @returns {Object} Settlement summary
 */
const getSettlementSummary = async (filters = {}) => {
  const { vendorId, startDate, endDate } = filters;

  // Build the query
  const query = {
    status: shipmentStatusValue.DELIVERED,
    vendorModel: 'Vendor'
  };

  if (vendorId) {
    query.vendor = new mongoose.Types.ObjectId(vendorId);
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  // Get total delivered orders
  const totalDeliveredOrders = await ProductShipment.countDocuments({
    ...query
  });

  // Get total sales value
  const totalSalesAggregation = await ProductShipment.aggregate([
    {
      $match: query
    },
    {
      $unwind: '$orderItems'
    },
    {
      $group: {
        _id: null,
        totalSales: { $sum: '$orderItems.vendorSubtotal' }
      }
    }
  ]);

  const totalSalesValue = totalSalesAggregation.length > 0 ? totalSalesAggregation[0].totalSales : 0;

  // Get amount pending settlement
  const pendingSettlementAggregation = await ProductShipment.aggregate([
    {
      $match: {
        ...query,
        $or: [
          { settlementStatus: settlementStatusValue.PENDING },
          { settlementStatus: { $exists: false } }
        ]
      }
    },
    {
      $unwind: '$orderItems'
    },
    {
      $group: {
        _id: null,
        pendingAmount: { $sum: '$orderItems.vendorSubtotal' }
      }
    }
  ]);

  const amountPendingSettlement = pendingSettlementAggregation.length > 0 ? pendingSettlementAggregation[0].pendingAmount : 0;

  // Get amount settled
  const settledAggregation = await ProductShipment.aggregate([
    {
      $match: {
        ...query,
        settlementStatus: settlementStatusValue.SETTLED
      }
    },
    {
      $unwind: '$orderItems'
    },
    {
      $group: {
        _id: null,
        settledAmount: { $sum: '$orderItems.vendorSubtotal' }
      }
    }
  ]);

  const amountSettled = settledAggregation.length > 0 ? settledAggregation[0].settledAmount : 0;

  return {
    totalDeliveredOrders,
    totalSalesValue,
    amountPendingSettlement,
    amountSettled
  };
};

/**
 * Settle shipments for a vendor
 * @param {Array} shipmentIds - Array of shipment IDs to settle
 * @param {String} adminId - Admin ID
 * @param {Object} data - Settlement data
 * @returns {Object} Result of settlement operation
 */
const settleShipments = async (shipmentId, adminId, data = {}) => {
  const { notes } = data;

  if (!shipmentId) {
    throwBadRequestError('No shipments selected for settlement');
  }

  // Check if shipments exist and are eligible for settlement
  const shipment = await ProductShipment.findOne({
    _id: shipmentId,
    status: shipmentStatusValue.DELIVERED,
    $or: [
      { settlementStatus: settlementStatusValue.PENDING },
      { settlementStatus: { $exists: false } }
    ]
  });

  if (!shipment) {
    throwBadRequestError('No eligible shipments found for settlement');
  }

  // Update shipments in a transaction
  const updatedShipments = await ProductShipment.updateOne(
    {
      _id: shipmentId
    },
    {
      $set: {
        settlementStatus: settlementStatusValue.SETTLED,
        settledAt: new Date(),
        settlementNotes: notes,
        settledBy: adminId
      }
    }
  );

  return {
    success: true,
    message: 'Shipments settled successfully',
    count: updatedShipments
  };
};

/**
 * Generate a commission invoice for a shipment
 * @param {Object} data - Invoice data
 * @param {String} adminId - Admin ID
 * @returns {Object} Generated invoice
 */
const generateInvoice = async (data, adminId) => {
  const { shipmentId } = data;

  // Check if shipment exists and is eligible for invoice generation
  const shipment = await ProductShipment.findOne({
    _id: shipmentId,
    status: shipmentStatusValue.DELIVERED,
    settlementStatus: settlementStatusValue.SETTLED
  }).populate('order');

  if (!shipment) {
    throwBadRequestError('Shipment not found or not eligible for invoice generation');
  }

  const isInvoiceExists = await Invoice.findOne({
    shipment: shipmentId
  });

  if (isInvoiceExists) {
    return {
      message: 'Invoice already generated',
      data: {
        invoice: isInvoiceExists,
        invoiceUrl: isInvoiceExists.invoiceFile
      }
    };
  }

  // Get vendor details
  const vendor = await Vendor.findById(shipment.vendor);

  if (!vendor || shipment.vendorModel !== 'Vendor') {
    throwNotFoundError('Vendor not found or shipment does not belong to a vendor');
  }

  // Get admin details for platform information
  const admin = await Admin.findById(adminId);

  if (!admin) {
    throwNotFoundError('Admin not found');
  }

  // Format order items for the invoice
  const orderItems = shipment.orderItems.map(item => {
    return {
      name: item.name['en'] || item.name,
      quantity: item.quantity,
      price: (item.vendorPrice || item.price).toFixed(2),
      subtotal: (item.vendorSubtotal || item.subtotal).toFixed(2)
    };
  });

  // Calculate commission amount (total sales value)
  let commissionAmount = 0;

  shipment.orderItems.forEach(item => {
    commissionAmount += (item.vendorSubtotal || item.subtotal);
  });

  // Calculate tax (18% GST)
  const taxRate = 0.18;
  const taxAmount = commissionAmount * taxRate;
  const totalAmount = commissionAmount + taxAmount;

  // Generate invoice number
  const invoiceNumber = `OG-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  // Get settlement period from shipment
  const settlementPeriod = {
    startDate: shipment.settledAt || shipment.deliveryDate || shipment.createdAt,
    endDate: shipment.settledAt || shipment.deliveryDate || shipment.createdAt
  };

  // Prepare invoice data
  const invoiceData = {
    invoiceNumber,
    invoiceDate: new Date(),
    vendorDetails: {
      businessName: vendor.businessName.en || vendor.businessName,
      address: vendor.address.fullAddress + ', ' + vendor.address.pincode,
      gstNumber: vendor.gstNumber
    },
    platformDetails: {
      businessName: 'One God Platform', // Replace with actual platform name from config
      address: 'One God Headquarters, India', // Replace with actual platform address from config
      gstNumber: 'GSTIN12345678901' // Replace with actual platform GSTIN from config
    },
    settlementPeriod,
    commissionAmount: commissionAmount.toFixed(2),
    taxAmount: taxAmount.toFixed(2),
    totalAmount: totalAmount.toFixed(2),
    orderNumber: shipment.order ? shipment.order.orderNumber : 'N/A',
    orderItems
  };

  // Generate PDF and get S3 key
  const invoiceFileKey = await generateCommissionInvoice(invoiceData);

  // Create invoice record in database
  const invoice = await Invoice.create({
    invoiceNumber,
    invoiceDate: new Date(),
    vendor: vendor._id,
    vendorDetails: {
      businessName: vendor.businessName.en || vendor.businessName,
      address: vendor.address.fullAddress + ', ' + vendor.address.pincode,
      gstNumber: vendor.gstNumber
    },
    platformDetails: {
      businessName: 'One God Platform', // Replace with actual platform name from config
      address: 'One God Headquarters, India', // Replace with actual platform address from config
      gstNumber: 'GSTIN12345678901' // Replace with actual platform GSTIN from config
    },
    settlementPeriod,
    commissionAmount,
    taxAmount,
    totalAmount,
    shipment: shipmentId,
    orderItems,
    invoiceFile: process.env.MEDIA_URL + '/' + invoiceFileKey,
    createdBy: adminId
  });

  return {
    message: messages.INVOICE_GENERATED,
    data: {
      invoice,
      invoiceUrl: process.env.MEDIA_URL + '/' + invoiceFileKey
    },
  };
};

module.exports = {
  getShipmentsForSettlement,
  getSettlementSummary,
  settleShipments,
  generateInvoice
};
