const Helicopter = require('../../../models/Helicopter');
const { throwBadRequestError } = require('../../../errors');
const { translateDataForStore } = require('../../../utils/translateInput');

const createHelicopter = async (helicopterData, adminId) => {
  // Process images if provided
  if (helicopterData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = helicopterData.image.startsWith(process.env.MEDIA_URL) ? 
      helicopterData.image : 
      `${process.env.MEDIA_URL}/${helicopterData.image}`;

    helicopterData.image = cloudFrontUrl;
  }

  // Process video if provided
  if (helicopterData.video) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = helicopterData.video.startsWith(process.env.MEDIA_URL) ? 
      helicopterData.video : 
      `${process.env.MEDIA_URL}/${helicopterData.video}`;

    helicopterData.video = cloudFrontUrl;
  }

  // Process itinerary if provided
  if (helicopterData.itinerary) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = helicopterData.itinerary.startsWith(process.env.MEDIA_URL) ? 
      helicopterData.itinerary : 
      `${process.env.MEDIA_URL}/${helicopterData.itinerary}`;

    helicopterData.itinerary = cloudFrontUrl;
  }

  // Translate fields
  const translatedFields = [ 'name', 'shortDescription', 'detailedDescription' ];
  const translatedData = await translateDataForStore(translatedFields, helicopterData);

  translatedFields.forEach(field => {
    if (helicopterData[field]) {
      delete helicopterData[field];
    }
  });

  helicopterData = {
    ...helicopterData,
    ...translatedData
  };

  // Create helicopter
  const helicopter = await Helicopter.create({
    ...helicopterData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return helicopter;
};

const listHelicopters = async (query) => {
  const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = -1, status = 'ACTIVE', startDate, endDate } = query;
  const skip = (page - 1) * limit;

  const filter = {
    status
  };

  const language = { code: 'en' }; // Assume this comes from request or default

  // Search in localized name
  if (search) {
    filter[`name.${language.code}`] = { $regex: search, $options: 'i' };
  }

  // Date range filtering logic
  if (startDate || endDate) {
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;

    // Ensure that the startDate is not later than the endDate
    if (start && end && start > end) {
      throw new Error('Start date cannot be greater than end date.');
    }

    // Build date filter based on the range
    const dateFilter = {};

    if (start) {
      dateFilter.$gte = start;
    }
    if (end) {
      dateFilter.$lte = end;
    }

    // Filter helicopters where dates overlap with the range
    filter.dates = dateFilter;
  }

  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder);

  const helicopters = await Helicopter.find(filter)
    .skip(skip)
    .limit(limit)
    .sort(sortOptions)
    .collation({ locale: 'en', strength: 1 });

  const total = await Helicopter.countDocuments(filter);

  const response = helicopters.map((helicopter) => ({
    ...helicopter.toObject(),
    name: helicopter.name?.en || '',
    shortDescription: helicopter.shortDescription?.en || '',
    detailedDescription: helicopter.detailedDescription?.en || '',
  }));

  return {
    helicopters: response,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      limit: parseInt(limit)
    }
  };
};

const getHelicopterById = async (helicopterId) => {
  const helicopter = await Helicopter.findById(helicopterId);

  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  helicopter.name = helicopter.name?.en || '';
  helicopter.shortDescription = helicopter.shortDescription?.en || '';
  helicopter.detailedDescription = helicopter.detailedDescription?.en || '';

  return helicopter;
};

const updateHelicopter = async (helicopterId, updateData, adminId) => {
  const helicopter = await Helicopter.findById(helicopterId);
  
  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt || helicopter.status === 'INACTIVE') {
    throwBadRequestError('Helicopter already deleted or inactive');
  }

  // Process images if provided
  if (updateData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = updateData.image.startsWith(process.env.MEDIA_URL) ? 
      updateData.image : 
      `${process.env.MEDIA_URL}/${updateData.image}`;

    updateData.image = cloudFrontUrl;
  }

  // Process video if provided
  if (updateData.video) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = updateData.video.startsWith(process.env.MEDIA_URL) ? 
      updateData.video : 
      `${process.env.MEDIA_URL}/${updateData.video}`;

    updateData.video = cloudFrontUrl;
  }

  // Process itinerary if provided
  if (updateData.itinerary) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = updateData.itinerary.startsWith(process.env.MEDIA_URL) ? 
      updateData.itinerary : 
      `${process.env.MEDIA_URL}/${updateData.itinerary}`;

    updateData.itinerary = cloudFrontUrl;
  }

  // Translate fields if provided
  const translatedFields = [ 'name', 'shortDescription', 'detailedDescription' ];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  const formatDate = d => new Date(d).toISOString().split('T')[0];

  const dates = updateData.dates?.length > 0
    ? updateData.dates.map(d => new Date(d))
    : helicopter.dates.map(d => new Date(d));

  const availability = updateData.availability?.length > 0
    ? updateData.availability.map(a => ({
      date: new Date(a.date),
      availableSeats: a.availableSeats
    }))
    : helicopter.availability.map(a => ({
      date: new Date(a.date),
      availableSeats: a.availableSeats
    }));

  // Convert to strings for easier comparison
  const dateSet = new Set(dates.map(formatDate));
  const availabilitySet = new Set(availability.map(a => formatDate(a.date)));

  //* Check 1: selected dates must not include any blackout dates
  for (const dateStr of dateSet) {
    if (!availabilitySet.has(dateStr)) {
      throwBadRequestError(`Missing availability for selected date ${dateStr}. Please add availability for this date.`);
    }
  }

  //* Check 2: Availability dates must be within selected dates
  for (const dateStr of availabilitySet) {
    if (!dateSet.has(dateStr)) {
      throwBadRequestError(`Availability date ${dateStr} is not selected. Please remove it from availability.`);
    }
  }

  // Update helicopter
  const updatedHelicopter = await Helicopter.findByIdAndUpdate(
    helicopterId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  );

  return updatedHelicopter;
};

const deleteHelicopter = async (helicopterId) => {
  const helicopter = await Helicopter.findById(helicopterId);
  
  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt) {
    throwBadRequestError('Helicopter already deleted');
  }

  //* Soft delete helicopter 
  helicopter.deletedAt = new Date();
  helicopter.status = 'INACTIVE';
  await helicopter.save();

  return helicopter;
};

const updateHelicopterStatus = async (helicopterId, body) => {
  const { status } = body;

  const helicopter = await Helicopter.findById(helicopterId);
  
  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt) {
    throwBadRequestError('Helicopter already deleted');
  }

  // Update helicopter status
  helicopter.status = status;
  await helicopter.save();

  return helicopter;
};

module.exports = {
  createHelicopter,
  listHelicopters,
  getHelicopterById,
  updateHelicopter,
  deleteHelicopter,
  updateHelicopterStatus
};
