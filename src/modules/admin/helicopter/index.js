const express = require('express');
const router = express.Router();
const helicopterController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.post('/create', auth, isAdminOrSuperAdmin, helicopterController.createHelicopter);
router.get('/list', auth, isAdminOrSuperAdmin, helicopterController.listHelicopters);
router.get('/:id', auth, isAdminOrSuperAdmin, helicopterController.getHelicopterById);
router.put('/:id', auth, isAdminOrSuperAdmin, helicopterController.updateHelicopter);
router.delete('/:id', auth, isAdminOrSuperAdmin, helicopterController.deleteHelicopter);
router.post('/upload-url', auth, isAdminOrSuperAdmin, helicopterController.getUploadUrl);
router.patch('/:id/status', auth, isAdminOrSuperAdmin, helicopterController.updateHelicopterStatus);

module.exports = router;