const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const { messages } = require('../../messages');
const eventService = require('./service');
const { createEventSchema, updateEventSchema, listEventsSchema, validateId, uploadUrlSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { getPresignedUrl, deleteFile } = require('../../utils/s3Service');
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');

/**
 * Create a new event
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createEvent = async (req, res) => {
  try {
    const { error } = createEventSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const event = await eventService.createEvent(req.body, req.user.id);

    //* Save audit log 
    const detail = `Event ${event.name} created successfully`;
    const model = 'Event';

    await saveAuditLog(req, req.user.id, auditLogAction.EVENT_CREATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENT_CREATED,
      status: true,
      data: event
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get event by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getEventById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const event = await eventService.getEventById(req.params.id, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENT_RETRIEVED,
      status: true,
      data: event
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update an event
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const updateEvent = async (req, res) => {
  try {
    const idValidation = validateId.validate(req.params.id);

    if (idValidation.error) {
      return res.status(400).json({ message: idValidation.error.details[0].message });
    }

    const { error } = updateEventSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const event = await eventService.updateEvent(req.params.id, req.body, req.user.id);

    //* Save audit log 
    const detail = `Event ${event.name} updated successfully`;
    const model = 'Event';

    await saveAuditLog(req, req.user.id, auditLogAction.EVENT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENT_UPDATED,
      status: true,
      data: event
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete an event
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteEvent = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const event = await eventService.deleteEvent(req.params.id);

    //* Save audit log 
    const detail = `Event ${event.name} deleted successfully`;
    const model = 'Event';

    await saveAuditLog(req, req.user.id, auditLogAction.EVENT_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENT_DELETED,
      status: true,
      data: event
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List events with filtering and pagination
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listEvents = async (req, res) => {
  try {
    const { error } = listEventsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await eventService.listEvents(req.query, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENTS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get events for homepage
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getHomepageEvents = async (req, res) => {
  try {
    const events = await eventService.getHomepageEvents(req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.HOMEPAGE_EVENTS_RETRIEVED,
      status: true,
      data: events
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get presigned URL for uploading event poster
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }
    
    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'events');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.UPLOAD_URL_GENERATED,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete an event poster image
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteImage = async (req, res) => {
  try {
    const { key } = req.params;

    await deleteFile(key);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENT_IMAGE_DELETED,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createEvent,
  getEventById,
  updateEvent,
  deleteEvent,
  listEvents,
  getHomepageEvents,
  getUploadUrl,
  deleteImage
};
