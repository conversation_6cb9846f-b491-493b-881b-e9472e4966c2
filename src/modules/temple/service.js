const bcrypt = require('bcryptjs');
const Temple = require('../../models/Temple');
const TempleAdmin = require('../../models/TempleAdmin');
const DarshanSchedule = require('../../models/DarshanSchedule');
const PoojaSchedule = require('../../models/PoojaSchedule');
const Event = require('../../models/Event');
const { throwBadRequestError } = require('../../errors');
const { messages } = require('../../messages');
const { generatePassword } = require('../../utils/passwordGenerator');
// const { sendWhatsAppMessage } = require('../../utils/whatsappService');
const FavoriteTemple = require('../../models/FavoriteTemple');
const Kiosk = require('../../models/Kiosk');
const KioskUser = require('../../models/KioskUser');
const LiveDarshan = require('../../models/LiveDarshan');
const Pujari = require('../../models/Pujari');
const Offering = require('../../models/Offering');
const Booking = require('../../models/Booking');
const { type, status } = require('../../constants/dbEnums');
const { emailSubjects } = require('../../messages/emailSubjects');
const path = require('path');
const { sendMail } = require('../../utils/sendMail');
const { transformTranslatedFields } = require('../../utils/localizer');
const { translateDataForStore } = require('../../utils/translateInput');
const User = require('../../models/User');
const Language = require('../../models/Language');
const moment = require('moment');

const processImages = (images) => {
  return images.map(image => {
    // If the image string already contains the MEDIA_URL, return as is
    if (image.startsWith(process.env.MEDIA_URL)) {
      return image;
    }
    // Otherwise, prepend the MEDIA_URL
    return `${process.env.MEDIA_URL}/${image}`;
  });
};

const createTemple = async (templeData) => {
  // Check if temple admin email or phone number is unique
  const existingAdmin = await TempleAdmin.findOne({
    $or: [
      { email: templeData.templeAdmin.email },
      { phoneNumber: templeData.templeAdmin.phoneNumber }
    ]
  });

  if (existingAdmin) {
    if (existingAdmin.email === templeData.templeAdmin.email) {
      throwBadRequestError(messages.EMAIL_ALREADY_EXISTS);
    }
    throwBadRequestError(messages.PHONE_NUMBER_ALREADY_EXISTS);
  }

  // Generate temporary password
  const tempPassword = generatePassword();
  const hashedPassword = await bcrypt.hash(tempPassword, 10);

  // Process images if provided
  if (templeData.images && templeData.images.length > 0) {
    templeData.images = processImages(templeData.images);
  }

  const translatedFields = [
    'name',
    'tagLine',
    'description',
    'history',
    'significanceAndArchitecture',
    'deity',
    'guidelines',
    'city',
    'state'
  ];

  const translatedData = await translateDataForStore(translatedFields, templeData);

  translatedFields.forEach(field => {
    delete templeData[field];
  });

  templeData = {
    ...templeData,
    ...translatedData
  };

  // Create temple
  const temple = await Temple.create({
    ...templeData,
  });

  // Create temple admin
  const templeAdmin = await TempleAdmin.create({
    firstName: templeData.templeAdmin.firstName,
    lastName: templeData.templeAdmin.lastName,
    email: templeData.templeAdmin.email,
    phoneNumber: templeData.templeAdmin.phoneNumber,
    whatsappEnabled: templeData.templeAdmin.whatsappEnabled || false,
    password: hashedPassword,
    passwordChangeRequired: true,
    temple: temple._id
  });

  // Send credentials via email
  const email = templeData.templeAdmin.email;
  const subject = emailSubjects.TEMPLE_ADMIN_ACCOUNT_CREDENTIALS;
  const templatePath = path.join(__dirname, '../../views/templeAdminLoginInfo.html');
  const data = {
    name: `${templeData.templeAdmin.firstName} ${templeData.templeAdmin.lastName}`,
    username: templeData.templeAdmin.email,
    password: tempPassword,
    templeName: temple.name || '',
    loginUrl: '',
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS
  };

  await sendMail(email, subject, templatePath, data);

  // // Send WhatsApp message if phone number is provided
  // if (templeAdmin.phoneNumber && templeAdmin.whatsappEnabled) {
  //   sendWhatsAppMessage(templeAdmin.phoneNumber, {
  //     username: templeAdmin.email,
  //     password: tempPassword
  //   });
  // }

  // TODO: Need to remove password from response
  const templeObj = temple.toObject();

  templeAdmin.password = tempPassword;
  templeObj.templeAdmins = templeAdmin;

  return templeObj;
};

//* Function to check if there are active bookings or events for a temple
const checkActiveBookingsOrEvents = async (templeId) => {
  const now = new Date();

  now.setUTCHours(0, 0, 0, 0); // Midnight UTC for date-only comparison

  const bookings = await Booking.find({
    temple: templeId,
    status: status.COMPLETED,
    $or: [
      {
        type: { $in: [ type.PHYSICAL_DARSHAN, type.PHYSICAL_POOJA, type.VIRTUAL_POOJA ] },
        date: { $gte: now }
      },
      {
        type: type.EVENT,
        'eventDates': {
          $elemMatch: { date: { $gte: now } }
        }
      }
    ]
  });

  const hasUpcomingBooking = bookings.some((booking) => {
    if (booking.type !== type.EVENT) {
      const bookingDate = new Date(booking.date);

      if (bookingDate.getTime() > now.getTime()) {
        return true;
      }

      // Booking is for today, check time
      const [ time, period ] = booking.timeSlot.endTime.split(' ');
      const [ hours, minutes ] = time.split(':');
      let h = parseInt(hours);

      if (period === 'PM' && h !== 12) {
        h += 12;
      }
      if (period === 'AM' && h === 12) {
        h = 0;
      }

      const bookingEndTime = new Date(now);

      bookingEndTime.setHours(h, parseInt(minutes), 0, 0);

      return bookingEndTime > new Date();
    } else {
      // Handle Event booking
      const todayMatch = booking.eventDates.find((event) => {
        return new Date(event.date).getTime() === now.getTime();
      });

      if (todayMatch) {
        const [ time, period ] = booking.timeSlot.endTime.split(' ');
        const [ hours, minutes ] = time.split(':');
        let h = parseInt(hours);

        if (period === 'PM' && h !== 12) {
          h += 12;
        }
        if (period === 'AM' && h === 12) {
          h = 0;
        }

        const eventEndTime = new Date(now);

        eventEndTime.setHours(h, parseInt(minutes), 0, 0);

        return eventEndTime > new Date();
      }

      // If today not found, but future dates exist, count as upcoming
      return booking.eventDates.some((event) => new Date(event.date).getTime() > now.getTime());
    }
  });

  return hasUpcomingBooking;
};

const updateTemple = async (templeId, updateData) => {
  const temple = await Temple.findById(templeId);

  if (!temple) {
    throwBadRequestError(messages.TEMPLE_NOT_FOUND);
  }

  if (updateData.promotionalKitCost === '') {
    updateData.promotionalKitCost = null;
  }

  const hasUpcomingBookingOrEvent = await checkActiveBookingsOrEvents(templeId);

  if (hasUpcomingBookingOrEvent) {
    throwBadRequestError(`Cannot update Temple ${temple.name} as it has active bookings or events.`);
  }

  // Process images if provided
  if (updateData.images && updateData.images.length > 0) {
    updateData.images = processImages(updateData.images);
  }

  // Handle temple admins separately if included in update
  if (updateData.templeAdmins) {
    const adminUpdates = updateData.templeAdmins;

    delete updateData.templeAdmins;

    // Process each admin update
    for (const adminData of adminUpdates) {
      if (adminData.email) {
        const existingAdmin = await TempleAdmin.findOne({
          $or: [
            { email: adminData.email },
            { phoneNumber: adminData.phoneNumber }
          ],
          temple: templeId
        });

        if (existingAdmin) {
          // Update existing admin
          // First check if phone number is being updated and is unique
          if (adminData.phoneNumber && adminData.phoneNumber !== existingAdmin.phoneNumber) {
            const existingAdminWithPhone = await TempleAdmin.findOne({
              phoneNumber: adminData.phoneNumber,
              _id: { $ne: existingAdmin._id }
            });

            // eslint-disable-next-line max-depth
            if (existingAdminWithPhone) {
              throwBadRequestError(messages.PHONE_NUMBER_ALREADY_EXISTS);
            }
          }

          await TempleAdmin.findByIdAndUpdate(
            existingAdmin._id,
            {
              $set: {
                firstName: adminData.firstName,
                lastName: adminData.lastName,
                phoneNumber: adminData.phoneNumber,
                whatsappEnabled: adminData.whatsappEnabled || false,
                status: adminData.status
              }
            },
            { new: true }
          );
        } else {
          // Check if phone number exists for new admin
          const existingAdminWithPhone = await TempleAdmin.findOne({
            phoneNumber: adminData.phoneNumber
          });

          if (existingAdminWithPhone) {
            throwBadRequestError(messages.PHONE_NUMBER_ALREADY_EXISTS);
          }

          // Generate temporary password for new admin
          const tempPassword = generatePassword();
          const hashedPassword = await bcrypt.hash(tempPassword, 10);

          // Create new admin
          const newAdmin = await TempleAdmin.create({
            firstName: adminData.firstName,
            lastName: adminData.lastName,
            email: adminData.email,
            phoneNumber: adminData.phoneNumber,
            whatsappEnabled: adminData.whatsappEnabled || false,
            password: hashedPassword,
            passwordChangeRequired: true,
            temple: templeId
          });

          // Send credentials via email
          // sendCredentialsEmail(newAdmin.email, {
          //   username: newAdmin.email,
          //   password: tempPassword,
          //   name: `${newAdmin.firstName} ${newAdmin.lastName}`
          // });

          // // Send WhatsApp message if enabled
          // if (newAdmin.phoneNumber && newAdmin.whatsappEnabled) {
          //   sendWhatsAppMessage(newAdmin.phoneNumber, {
          //     username: newAdmin.email,
          //     password: tempPassword
          //   });
          // }
        }
      }
    }
  }

  // Translate fields if provided
  const translatedFields = [
    'name',
    'tagLine',
    'description',
    'history',
    'significanceAndArchitecture',
    'deity',
    'guidelines',
    'city',
    'state'
  ];

  // Check if any translatable fields are being updated
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update temple document
  Object.assign(temple, updateData);
  await temple.save();

  // Get all temple admins
  const templeAdmins = await TempleAdmin.find({ temple: templeId })
    .select('-password');

  // Combine temple data with admins
  return {
    ...temple.toObject(),
    templeAdmins
  };
};

const deleteTemple = async (templeId) => {
  //* 1. Check if temple exists
  const temple = await Temple.findOne({
    _id: templeId,
    deletedAt: null
  });

  if (!temple) {
    throwBadRequestError('Temple not found or already deleted');
  }

  //* 2. Check for active bookings or events
  const isActiveBookingOrEvent = await checkActiveBookingsOrEvents(templeId);

  if (isActiveBookingOrEvent) {
    throwBadRequestError(`Cannot delete Temple ${temple.name} as it has active bookings or events.`);
  }

  //* 3. Delete associated darshanSchedules (soft delete)
  await DarshanSchedule.updateMany(
    { temple: templeId },
    { deletedAt: new Date(), status: 'INACTIVE' }
  );

  //* 4. Delete associated poojaSchedules (soft delete)
  await PoojaSchedule.updateMany(
    { temple: templeId },
    { deletedAt: new Date(), status: 'INACTIVE' }
  );

  //* 5. Delete associated events (soft delete)
  await Event.updateMany(
    { temple: templeId },
    { deletedAt: new Date(), status: 'INACTIVE' }
  );

  //* 6. Delete associated Live Darshans (soft delete)
  await LiveDarshan.updateMany(
    { temple: templeId },
    { deletedAt: new Date() }
  );

  //* 7. Delete associated Pujaris (soft delete)
  await Pujari.updateMany(
    { temple: templeId },
    { deletedAt: new Date(), status: 'INACTIVE' }
  );

  //* 8. Delete associated Offerings (soft delete)
  await Offering.updateMany(
    { temple: templeId },
    { deletedAt: new Date(), isActive: false }
  );

  //* 9. Delete associated Kiosks (soft delete)
  const kiosk = await Kiosk.findOneAndUpdate(
    { temple: templeId },
    { deletedAt: new Date(), status: 'INACTIVE' },
    { new: true }
  );

  //* 10. Delete associated Kiosk Users (soft delete)
  if (kiosk) {
    await KioskUser.updateMany(
      { kiosk: kiosk._id },
      { deletedAt: new Date(), status: 'INACTIVE' }
    );
  }

  //* 11. Delete associated Temple Admins (soft delete)
  await TempleAdmin.updateMany(
    { temple: templeId },
    { deletedAt: new Date() }
  );

  //* 12. Delete temple (soft delete)
  await Temple.updateOne(
    { _id: templeId },
    { deletedAt: new Date() }
  );

  return temple;
};

const getAllTemples = async (query = {}) => {
  const {
    search,
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = -1,
    states,
    cities,
    tags,
    deities,
    services,
    category
  } = query;

  // Set default language and get user's preferred language
  const language = { code: 'en' }; // Default to English

  const filter = {
    deletedAt: null
  };

  if (states) {
    filter[`state.${language.code}`] = { $in: states.split(',') };
  }

  if (cities) {
    filter[`city.${language.code}`] = { $in: cities.split(',') };
  }

  if (tags) {
    filter.tags = { $in: tags.split(',') };
  }

  if (deities) {
    filter[`deity.${language.code}`] = { $in: deities.split(',') };
  }

  if (services) {
    filter.services = { $in: services.split(',') };
  }

  if (category) {
    filter.category = category;
  }

  // Search by name or deity using substring match
  if (search) {
    filter.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`deity.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  // Handle sorting
  const sort = {};

  sort[sortBy] = parseInt(sortOrder) || -1;

  // Calculate skip value for pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Get temples with pagination
  const temples = await Temple.find(filter)
    .populate({
      path: 'category',
      select: 'name'
    })
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit)).lean();

  // Get total count for pagination
  const total = await Temple.countDocuments(filter);

  if (temples.length) {
    const localizedTemples = await transformTranslatedFields(temples, language.code);

    return {
      temples: localizedTemples,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  }

};

const getTempleById = async (templeId, userId) => {
  const temple = await Temple.findById(templeId).lean();

  if (!temple || temple.deletedAt) {
    throwBadRequestError(messages.TEMPLE_NOT_FOUND);
  }

  const favouriteTemple = await FavoriteTemple.findOne({ temple: templeId, user: userId });

  //* Get upcoming events
  const now = new Date();

  now.setUTCHours(0, 0, 0, 0);

  //* First fetch all potential relevant events
  const events = await Event.find({
    temple: templeId,
    status: 'ACTIVE',
    $or: [
      //? SPECIFIC_DATE
      //* If event date is greater than today's date
      { dateType: 'SPECIFIC_DATE', specificDate: { $gte: now } },

      //? DATE_RANGE
      //* If event startDate is greater than today's date
      { dateType: 'DATE_RANGE', 'dateRange.startDate': { $gte: now } },

      // //* If event startDate is less than today's date but endDate is greater than today's date
      { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: now } },
    ]
  });

  //* Filter events in JavaScript using proper time comparison
  const upcomingEvents = events.filter(event => {
    if (event.dateType === 'SPECIFIC_DATE') {
      const eventDate = new Date(event.specificDate);

      // Case 1: If not today's event, include it (as it's a future event)
      if (eventDate.getTime() !== now.getTime()) {
        return true;
      }

      // Case 2: For today's events, check if end time is in future
      const [ time, period ] = event.endTime.split(' ');
      const [ hours, minutes ] = time.split(':');
      let eventHours = parseInt(hours);

      // Convert to 24-hour format
      if (period === 'PM' && eventHours !== 12) {
        eventHours += 12;
      }
      if (period === 'AM' && eventHours === 12) {
        eventHours = 0;
      }

      const eventEndTime = new Date(now);

      eventEndTime.setHours(eventHours, parseInt(minutes), 0, 0);
      const currentTime = new Date();

      return eventEndTime > currentTime;
    } else {
      const endDate = new Date(event.dateRange.endDate);

      // Case 1: If not today's event, include it (as it's within valid range)
      if (endDate.getTime() !== now.getTime()) {
        return true;
      }

      // Case 2: For today's events, check if end time is in future
      const [ time, period ] = event.endTime.split(' ');
      const [ hours, minutes ] = time.split(':');
      let eventHours = parseInt(hours);

      // Convert to 24-hour format
      if (period === 'PM' && eventHours !== 12) {
        eventHours += 12;
      }
      if (period === 'AM' && eventHours === 12) {
        eventHours = 0;
      }
      const eventEndTime = new Date(now);

      eventEndTime.setHours(eventHours, parseInt(minutes), 0, 0);
      const currentTime = new Date();

      return eventEndTime > currentTime;
    }
  });

  const result = { ...temple, isFavourite: !!favouriteTemple, upcomingEvents };

  let language = { code: 'en' }; // Default to English

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  return await transformTranslatedFields(result, language.code);
};

const getDashboardTemples = async (user) => {
  // Set default language and get user's preferred language

  let language = { code: 'en' }; // Default to English

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const dashboardTemples = await Temple.find({ showOnHomepage: true, deletedAt: null })
    .sort({ updatedAt: -1 }).collation({ locale: 'en', strength: 1 }).lean();

  const otherTemples = await Temple.find({ showOnHomepage: false, deletedAt: null })
    .sort({ name: 1 }).collation({ locale: 'en', strength: 1 }).lean();

  // Localize temples
  const localizedDashboardTemples = await transformTranslatedFields(dashboardTemples, language.code);
  const localizedOtherTemples = await transformTranslatedFields(otherTemples, language.code);

  return {
    dashboardTemples: localizedDashboardTemples,
    otherTemples: localizedOtherTemples
  };
};

const updateDashboardTemples = async (templeIds) => {
  // First verify all temples exist
  const temples = await Temple.find({ _id: { $in: templeIds } });

  if (temples.length !== templeIds.length) {
    throwBadRequestError('One or more temple IDs are invalid');
  }

  // Reset all temples' showOnHomepage flag
  await Temple.updateMany({}, { showOnHomepage: false });

  // Set selected temples' showOnHomepage to true
  await Temple.updateMany(
    { _id: { $in: templeIds } },
    { showOnHomepage: true }
  );

  // Return updated dashboard temples
  return Temple.find({ _id: { $in: templeIds } });
};

const getTemplesDarshanSchedules = async (templeId, query = {}, user) => {
  const {
    page = 1,
    limit = 10,
  } = query;

  const filter = { temple: templeId, status: 'ACTIVE' };

  const schedules = await DarshanSchedule.find(filter)
    .populate([
      { path: 'temple', select: 'name location' },
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' }
    ])
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((page - 1) * limit)
    .limit(limit);

  const schedulesWithPricing = schedules.map(schedule => {
    const scheduleObj = schedule.toObject();

    // Calculate start pricing
    const startPricing = (scheduleObj.pricing.individual || 0);

    return {
      ...scheduleObj,
      startPricing
    };
  });

  const total = await DarshanSchedule.countDocuments(filter);

  let localizedSchedules = schedulesWithPricing;

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  localizedSchedules = await transformTranslatedFields(schedulesWithPricing, language.code);

  return {
    schedules: localizedSchedules,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    }
  };
};

const getTemplesPoojaSchedules = async (templeId, query = {}, user) => {
  const {
    page = 1,
    limit = 10,
    type,
  } = query;

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const filter = { temple: templeId, status: 'ACTIVE' };

  if (type) {
    filter.type = type;
  }

  const schedules = await PoojaSchedule.find(filter)
    .populate('temple')
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((page - 1) * limit)
    .limit(limit);

  const schedulesWithPricing = schedules.map(schedule => {
    const scheduleObj = schedule.toObject();

    // Calculate start pricing
    const startPricing = (scheduleObj.pricing.individual || 0);

    return {
      ...scheduleObj,
      startPricing
    };
  });

  const total = await PoojaSchedule.countDocuments(filter);

  // Localize schedules
  const localizedSchedules = await transformTranslatedFields(schedulesWithPricing, language.code);

  return {
    schedules: localizedSchedules,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * Check if there are darshan schedules, pooja schedules, or events available
 * from or after a specific date and time for a given temple
 * @param {string} templeId - The temple ID
 * @param {string} date - The date to check from (YYYY-MM-DD format)
 * @param {string} time - The time to check from (HH:MM AM/PM format)
 * @returns {Object} - Object with availability status for darshan, pooja, and events
 */
const checkAvailability = async (templeId, date, time) => {
  // Validate temple exists
  const temple = await Temple.findById(templeId);

  if (!temple) {
    throwBadRequestError(messages.TEMPLE_NOT_FOUND);
  }

  // Parse the input date and time
  const [ timePart, period ] = time.split(' ');
  const [ hours, minutes ] = timePart.split(':');
  let inputHours = parseInt(hours);

  // Convert to 24-hour format
  if (period === 'PM' && inputHours !== 12) {
    inputHours += 12;
  }
  if (period === 'AM' && inputHours === 12) {
    inputHours = 0;
  }

  // Create a Date object for the input date and time
  const inputDateTime = new Date(date);

  inputDateTime.setHours(inputHours, parseInt(minutes), 0, 0);

  // Set date without time for date-only comparisons
  const inputDate = new Date(date);

  inputDate.setUTCHours(0, 0, 0, 0);

  // Initialize result object
  const result = {
    isDarshanAvailable: false,
    isPhysicalPoojaAvailable: false,
    isEventAvailable: false,
    isVirtualPoojaAvailable: false
  };

  const [ darshanSchedules, physicalPoojaSchedules, virtualPoojaSchedules, events ] = await Promise.all([
    DarshanSchedule.find({
      temple: templeId,
      status: 'ACTIVE',
      $or: [
      // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    }),
    PoojaSchedule.find({
      temple: templeId,
      type: 'PHYSICAL',
      status: 'ACTIVE',
      $or: [
      // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    }),
    PoojaSchedule.find({
      temple: templeId,
      type: 'VIRTUAL',
      status: 'ACTIVE',
      $or: [
      // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    }),
    Event.find({
      temple: templeId,
      status: 'ACTIVE',
      $or: [
      // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    })
  ]);

  // Process darshan schedules
  if (darshanSchedules.length > 0) {
    for (const schedule of darshanSchedules) {
      // For schedules on the same day as input, check time slots
      if (schedule.dateType === 'SPECIFIC_DATE' &&
          new Date(schedule.specificDate).toDateString() === inputDate.toDateString()) {

        // Check if any time slot is after the input time
        const hasValidTimeSlot = schedule.timeSlots.some(slot => {
          const [ slotEndTime, slotPeriod ] = slot.endTime.split(' ');
          const [ slotHours, slotMinutes ] = slotEndTime.split(':');
          let endHours = parseInt(slotHours);

          // Convert to 24-hour format
          if (slotPeriod === 'PM' && endHours !== 12) {
            endHours += 12;
          }
          if (slotPeriod === 'AM' && endHours === 12) {
            endHours = 0;
          }

          const slotEndDateTime = new Date(inputDate);

          slotEndDateTime.setHours(endHours, parseInt(slotMinutes), 0, 0);

          return slotEndDateTime > inputDateTime;
        });

        if (hasValidTimeSlot) {
          result.isDarshanAvailable = true;
        }
      } else {
        // For future dates or date ranges, mark as available
        result.isDarshanAvailable = true;
      }

      // If we already found an available darshan, no need to check further
      if (result.isDarshanAvailable) {
        break;
      }
    }
  }

  // Process pooja schedules
  if (physicalPoojaSchedules.length > 0) {
    for (const schedule of physicalPoojaSchedules) {
      // For schedules on the same day as input, check time slots
      if (schedule.dateType === 'SPECIFIC_DATE' &&
          new Date(schedule.specificDate).toDateString() === inputDate.toDateString()) {

        // Check if any time slot is after the input time
        const hasValidTimeSlot = schedule.timeSlots.some(slot => {
          const [ slotEndTime, slotPeriod ] = slot.endTime.split(' ');
          const [ slotHours, slotMinutes ] = slotEndTime.split(':');
          let endHours = parseInt(slotHours);

          // Convert to 24-hour format
          if (slotPeriod === 'PM' && endHours !== 12) {
            endHours += 12;
          }
          if (slotPeriod === 'AM' && endHours === 12) {
            endHours = 0;
          }

          const slotEndDateTime = new Date(inputDate);

          slotEndDateTime.setHours(endHours, parseInt(slotMinutes), 0, 0);

          return slotEndDateTime > inputDateTime;
        });

        if (hasValidTimeSlot) {
          result.isPhysicalPoojaAvailable = true;
        }
      } else {
        // For future dates or date ranges, mark as available
        result.isPhysicalPoojaAvailable = true;
      }

      // If we already found an available pooja, no need to check further
      if (result.isPhysicalPoojaAvailable) {
        break;
      }
    }
  }

  if (virtualPoojaSchedules.length > 0) {
    for (const schedule of virtualPoojaSchedules) {
      // For schedules on the same day as input, check time slots
      if (schedule.dateType === 'SPECIFIC_DATE' &&
          new Date(schedule.specificDate).toDateString() === inputDate.toDateString()) {

        // Check if any time slot is after the input time
        const hasValidTimeSlot = schedule.timeSlots.some(slot => {
          const [ slotEndTime, slotPeriod ] = slot.endTime.split(' ');
          const [ slotHours, slotMinutes ] = slotEndTime.split(':');
          let endHours = parseInt(slotHours);

          // Convert to 24-hour format
          if (slotPeriod === 'PM' && endHours !== 12) {
            endHours += 12;
          }
          if (slotPeriod === 'AM' && endHours === 12) {
            endHours = 0;
          }

          const slotEndDateTime = new Date(inputDate);

          slotEndDateTime.setHours(endHours, parseInt(slotMinutes), 0, 0);

          return slotEndDateTime > inputDateTime;
        });

        if (hasValidTimeSlot) {
          result.isVirtualPoojaAvailable = true;
        }
      } else {
        // For future dates or date ranges, mark as available
        result.isVirtualPoojaAvailable = true;
      }

      // If we already found an available pooja, no need to check further
      if (result.isVirtualPoojaAvailable) {
        break;
      }
    }
  }

  // Process events
  if (events.length > 0) {
    for (const event of events) {
      // For events on the same day as input, check end time
      if (event.dateType === 'SPECIFIC_DATE' &&
          new Date(event.specificDate).toDateString() === inputDate.toDateString()) {

        // Check if event end time is after the input time
        const [ eventEndTime, eventPeriod ] = event.endTime.split(' ');
        const [ eventHours, eventMinutes ] = eventEndTime.split(':');
        let endHours = parseInt(eventHours);

        // Convert to 24-hour format
        if (eventPeriod === 'PM' && endHours !== 12) {
          endHours += 12;
        }
        if (eventPeriod === 'AM' && endHours === 12) {
          endHours = 0;
        }

        const eventEndDateTime = new Date(inputDate);

        eventEndDateTime.setHours(endHours, parseInt(eventMinutes), 0, 0);

        if (eventEndDateTime > inputDateTime) {
          result.isEventAvailable = true;
        }
      } else {
        // For future dates or date ranges, mark as available
        result.isEventAvailable = true;
      }

      // If we already found an available event, no need to check further
      if (result.isEventAvailable) {
        break;
      }
    }
  }

  return result;
};

const getTemplesByService = async (user, type, date, time, query = {}) => {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = -1,
    search
  } = query;

  let language = { code: 'en' }; // Default to English

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  // Parse the input date and time
  const [ timePart, period ] = time.split(' ');
  const [ hours, minutes ] = timePart.split(':');
  let inputHours = parseInt(hours);

  // Convert to 24-hour format
  if (period === 'PM' && inputHours !== 12) {
    inputHours += 12;
  }
  if (period === 'AM' && inputHours === 12) {
    inputHours = 0;
  }

  const inputDateTime = moment(date).format('YYYY-MM-DD') + 'T' + (inputHours < 10 ? '0' + inputHours : inputHours) + ':' + minutes + ':00.000Z';
  const inputDate = moment(date).format('YYYY-MM-DD') + 'T00:00:00.000Z';

  const templeIds = [];

  // Check for darshan schedules
  if (type === 'darshan') {
    const darshanSchedules = await DarshanSchedule.find({
      status: 'ACTIVE',
      deletedAt: null,
      $or: [
        // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    }).select('temple dateType specificDate dateRange timeSlots');

    // Process darshan schedules to get temple IDs
    for (const schedule of darshanSchedules) {
      // For schedules on the same day as input, check time slots
      if (schedule.dateType === 'SPECIFIC_DATE' &&
        moment(schedule.specificDate).format('YYYY-MM-DD') === moment(inputDate).format('YYYY-MM-DD')) {

        // Check if any time slot is after the input time
        const hasValidTimeSlot = schedule.timeSlots.some(slot => {
          const [ slotEndTime, slotPeriod ] = slot.endTime.split(' ');
          const [ slotHours, slotMinutes ] = slotEndTime.split(':');
          let endHours = parseInt(slotHours);

          // Convert to 24-hour format
          if (slotPeriod === 'PM' && endHours !== 12) {
            endHours += 12;
          }
          if (slotPeriod === 'AM' && endHours === 12) {
            endHours = 0;
          }

          const slotEndDateTime = moment(inputDate).format('YYYY-MM-DD') + 'T' + (endHours < 10 ? '0' + endHours : endHours) + ':' + slotMinutes + ':00.000Z';

          return slotEndDateTime > inputDateTime;
        });

        if (hasValidTimeSlot && !templeIds.includes(schedule.temple.toString())) {
          templeIds.push(schedule.temple.toString());
        }
      } else {
        // For future dates or date ranges, mark as available
        if (!templeIds.includes(schedule.temple.toString())) {
          templeIds.push(schedule.temple.toString());
        }
      }
    }
  }

  // Check for pooja schedules
  if (type === 'physical_pooja') {
    const poojaSchedules = await PoojaSchedule.find({
      status: 'ACTIVE',
      type: 'PHYSICAL',
      deletedAt: null,
      $or: [
        // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    }).select('temple dateType specificDate dateRange timeSlots');

    // Process pooja schedules to get temple IDs
    for (const schedule of poojaSchedules) {
      // For schedules on the same day as input, check time slots
      if (schedule.dateType === 'SPECIFIC_DATE' &&
        moment(schedule.specificDate).format('YYYY-MM-DD') === moment(inputDate).format('YYYY-MM-DD')) {

        // Check if any time slot is after the input time
        const hasValidTimeSlot = schedule.timeSlots.some(slot => {
          const [ slotEndTime, slotPeriod ] = slot.endTime.split(' ');
          const [ slotHours, slotMinutes ] = slotEndTime.split(':');
          let endHours = parseInt(slotHours);

          // Convert to 24-hour format
          if (slotPeriod === 'PM' && endHours !== 12) {
            endHours += 12;
          }
          if (slotPeriod === 'AM' && endHours === 12) {
            endHours = 0;
          }

          const slotEndDateTime = moment(inputDate).format('YYYY-MM-DD') + 'T' + (endHours < 10 ? '0' + endHours : endHours) + ':' + slotMinutes + ':00.000Z';

          return slotEndDateTime > inputDateTime;
        });

        if (hasValidTimeSlot && !templeIds.includes(schedule.temple.toString())) {
          templeIds.push(schedule.temple.toString());
        }
      } else {
        // For future dates or date ranges, mark as available
        if (!templeIds.includes(schedule.temple.toString())) {
          templeIds.push(schedule.temple.toString());
        }
      }
    }
  }

  if (type === 'virtual_pooja') {
    const poojaSchedules = await PoojaSchedule.find({
      status: 'ACTIVE',
      type: 'VIRTUAL',
      deletedAt: null,
      $or: [
        // SPECIFIC_DATE: date is equal to or after input date
        { dateType: 'SPECIFIC_DATE', specificDate: { $gte: inputDate } },

        // DATE_RANGE: end date is equal to or after input date
        { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: inputDate } }
      ]
    }).select('temple dateType specificDate dateRange timeSlots');

    // Process pooja schedules to get temple IDs
    for (const schedule of poojaSchedules) {
      // For schedules on the same day as input, check time slots
      if (schedule.dateType === 'SPECIFIC_DATE' &&
        moment(schedule.specificDate).format('YYYY-MM-DD') === moment(inputDate).format('YYYY-MM-DD')) {

        // Check if any time slot is after the input time
        const hasValidTimeSlot = schedule.timeSlots.some(slot => {
          const [ slotEndTime, slotPeriod ] = slot.endTime.split(' ');
          const [ slotHours, slotMinutes ] = slotEndTime.split(':');
          let endHours = parseInt(slotHours);

          // Convert to 24-hour format
          if (slotPeriod === 'PM' && endHours !== 12) {
            endHours += 12;
          }
          if (slotPeriod === 'AM' && endHours === 12) {
            endHours = 0;
          }

          const slotEndDateTime = moment(inputDate).format('YYYY-MM-DD') + 'T' + (endHours < 10 ? '0' + endHours : endHours) + ':' + slotMinutes + ':00.000Z';

          return slotEndDateTime > inputDateTime;
        });

        if (hasValidTimeSlot && !templeIds.includes(schedule.temple.toString())) {
          templeIds.push(schedule.temple.toString());
        }
      } else {
        // For future dates or date ranges, mark as available
        if (!templeIds.includes(schedule.temple.toString())) {
          templeIds.push(schedule.temple.toString());
        }
      }
    }
  }

  // If no temples found, return empty result
  if (templeIds.length === 0) {
    return {
      temples: [],
      pagination: {
        total: 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: 0
      }
    };
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Set up sorting
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  const filter = {
    _id: { $in: templeIds },
    deletedAt: null
  };

  // Search by name or deity using substring match
  if (search) {
    filter.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`deity.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  // Get temples with pagination
  const temples = await Temple.find(filter)
    .populate({
      path: 'category',
      select: 'name'
    })
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get total count for pagination
  const total = templeIds.length;

  // Localize temples
  const localizedTemples = await transformTranslatedFields(temples, language.code);

  return {
    temples: localizedTemples,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  createTemple,
  updateTemple,
  deleteTemple,
  getAllTemples,
  getTempleById,
  getDashboardTemples,
  updateDashboardTemples,
  getTemplesDarshanSchedules,
  getTemplesPoojaSchedules,
  checkAvailability,
  getTemplesByService
};
