const moment = require('moment');
const OneTimePassword = require('../../../models/OneTimePassword');
const { messages, emailSubjects } = require('../../../messages');
const { throwBadRequestError } = require('../../../errors');
const { otpTypeValue, userTypeValue } = require('../../../constants/dbEnums');
const { createOtp, findOtp, findAndUpdateOtp, deleteOtps, deleteOtp } = require('../../../database/queries/otp.query');
const { findUser } = require('../../../database/queries/user.query');
const { createToken } = require('../../../database/queries/accessToken.query');
const { generateToken } = require('../../../utils/jwt');
const Token = require('../../../models/Token');
const path = require('path');
const { sendMail } = require('../../../utils/sendMail');
const { sendSMS } = require('../../../utils/sms');

//* 3. Function to verify email
const verifyEmail = async (userObj) => {
  // Generate 6 digit OTP
  const otp = String(Math.floor(100000 + Math.random() * 900000));
  const expiredAt = moment().add(5, 'minutes').toDate();

  // Delete any existing OTP records for this email
  await OneTimePassword.deleteMany({
    email: userObj.email,
    type: otpTypeValue.SIGN_UP
  });

  // Create new OTP record
  await OneTimePassword.create({
    code: otp,
    email: userObj.email,
    type: otpTypeValue.SIGN_UP,
    expiresAt: expiredAt,
    isEmailVerified: false // Initially set to false
  });

  // Send OTP email
  const email = userObj.email;
  const subject = emailSubjects.VERIFY_ACCOUNT;
  const templatePath = path.join(__dirname, '../../../views/signUp.html');
  const data = {
    name: userObj.name || '',
    code: otp
  };

  await sendMail(email, subject, templatePath, data);

  return {
    message: messages.OTP_SENT,
    email: userObj.email
  };
};

/**
 * Function to send OTP to phone number
 * @param {*}
 * @returns empty array on success
 */
const sendOtp = async ({ phone, countryCode, preferredLanguage }) => {
  const otpCode = generateOTP();

  // const countryCodeValue = countryCode.replace('+', '') ?? '91';

  // const recipients = [
  //   {
  //     mobiles: `${countryCodeValue}${phone}`,
  //     var1: otpCode,
  //     var2: 5
  //   }
  // ];

  // const template = preferredLanguage === 'Hindi' ? process.env.HINDI_SMS_TEMPLATE_ID : process.env.ENGLISH_SMS_TEMPLATE_ID;

  await Promise.all([
    // sendSMS(recipients, template),
    deleteOtps({
      phoneNumber: phone,
      countryCode,
      type: otpTypeValue.PHONE_NUMBER,
      userType: userTypeValue.USER,
      deletedAt: null,
      isVerified: false,
    })
  ]);

  await createOtp({
    phoneNumber: phone,
    countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    otp: otpCode,
    userType: userTypeValue.USER,
    expiresAt: moment().add(5, 'minutes')
  });

  return [];
};

const generateOTP = () => Math.floor(100000 + Math.random() * 900000);

/**
 * Function to verify User OTP
 * @param {*} userObj
 * @returns Object containing message and phone number on success
 */
const verifyOtp = async (userObj) => {
  const otp = await findOtp({
    phoneNumber: userObj.phone,
    countryCode: userObj.countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    userType: userTypeValue.USER,
    deletedAt: null,
    isVerified: false,
  });

  if (!otp) {
    throwBadRequestError(messages.INVALID_OTP);
  }

  if (new Date(otp.expiresAt) < new Date()) {
    await deleteOtp({ _id: otp._id });
    throwBadRequestError(messages.OTP_EXPIRED);
  }

  const receivedOtp = String(userObj.otp).trim();
  const storedOtp = String(otp.otp).trim();

  if (process.env.APP_ENV && process.env.APP_ENV === 'development') {
    if (receivedOtp !== '797979' && receivedOtp !== storedOtp) {
      throwBadRequestError(messages.INVALID_OTP);
    }
  } else {
    if (receivedOtp !== storedOtp) {
      throwBadRequestError(messages.INVALID_OTP);
    }
  }

  await findAndUpdateOtp({ _id: otp._id }, { isVerified: true });

  const userExists = await findUser({
    phoneNumber: userObj.phone,
    countryCode: userObj.countryCode,
    deletedAt: null,
  });

  const response = {
    message: messages.PHONE_NUMBER_VERIFIED,
    phone: userObj.phone,
    countryCode: userObj.countryCode,
    accountExists: false,
  };

  if (userExists) {
    const token = await generateToken({ ...userExists , userType: userTypeValue.USER });

    const expireTime = process.env.JWT_EXPIRY;
    const days = expireTime.toLowerCase().replace('d', '');

    await createToken({
      token: token,
      userType: userTypeValue.USER,
      userId: userExists._id,
      fcmToken: userObj?.fcmToken,
      expiresAt: new Date().getTime() + (parseInt(days) * 24 * 60 * 60 * 1000),
    });

    response.accountExists = userExists;
    response.token = token;
  }

  return response;
};

const logout = async (userId, token, userType) => {
  return await Token.deleteOne({ 
    userId,
    token,
    userType
  });
};

module.exports = {
  verifyEmail,
  sendOtp,
  verifyOtp,
  logout,
};
