const Cart = require('../../../models/Cart');
const Product = require('../../../models/Product');
const ProductVariant = require('../../../models/ProductVariant');
const FavoriteProduct = require('../../../models/FavoriteProduct');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { productStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');

/**
 * Get user's cart
 */
const getCart = async (userId) => {
  let cart = await Cart.findOne({ user: userId })
    .populate({
      path: 'items.product',
      select: 'name price discountPrice images stock status',
      match: { status: productStatusValue.ACTIVE }
    });

  if (!cart) {
    // Create empty cart if none exists
    cart = await Cart.create({
      user: userId,
      items: [],
      subtotal: 0
    });
  }

  // Filter out any items where the product is null (inactive or deleted)
  cart.items = cart.items.filter(item => item.product);

  // Populate variants if needed
  if (cart.items.some(item => item.variant)) {
    await Cart.populate(cart, {
      path: 'items.variant',
      select: 'price discountPrice stock images attributes'
    });
  }

  // Recalculate subtotal and update stored prices
  cart.subtotal = cart.items.reduce((total, item) => {
    let price;

    if (item.variant && item.variant.price) {
      // Use variant price if available
      price = item.variant.discountPrice || item.variant.price;
    } else {
      // Fall back to product price
      price = item.product.discountPrice || item.product.price;
    }

    // Update the stored price to match current price
    item.price = price;

    return total + (price * item.quantity);
  }, 0);

  await cart.save();

  // Get favorite products for this user to add to cart items
  const favoriteProducts = await FavoriteProduct.find({ user: userId });

  // Create a map for quick lookup
  const favoriteProductMap = {};
  const favoriteVariantMap = {};

  favoriteProducts.forEach(favorite => {
    const productId = favorite.product.toString();

    if (favorite.variant) {
      // If it's a variant favorite
      const variantId = favorite.variant.toString();

      favoriteVariantMap[`${productId}-${variantId}`] = true;
    } else {
      // If it's a product favorite
      favoriteProductMap[productId] = true;
    }
  });

  // cart = cart.toObject();

  // Add isFavorite flag to each cart item
  cart.items.forEach(item => {
    const productId = item.product._id.toString();

    const variantId = item.variant ? item.variant._id.toString() : '';

    item._doc.isFavorite = !!favoriteVariantMap[`${productId}-${variantId}`];
    if (!item._doc.isFavorite) {
      item._doc.isFavorite = !!favoriteProductMap[productId];
    }
  });

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  cart = await transformTranslatedFields(cart.toObject({ getters: true }), language.code);

  return cart;
};

/**
 * Add item to cart
 */
const addToCart = async ({ userId, productId, variantId, quantity }) => {
  // Check if product exists and is active
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findOne({
    _id: productId,
    status: productStatusValue.ACTIVE
  });

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product has variants
  let variant = null;
  let variantAttributes = null;

  if (product.hasVariants) {
    if (!variantId) {
      // If product has variants but no variant ID provided, use default variant
      variant = await ProductVariant.findOne({
        product: productId,
        isDefault: true
      });

      if (!variant) {
        throwBadRequestError(messages.DEFAULT_VARIANT_NOT_FOUND);
      }
    } else {
      // Validate variant ID
      if (!mongoose.Types.ObjectId.isValid(variantId)) {
        throwBadRequestError(messages.INVALID_VARIANT_ID);
      }

      // Check if variant exists and belongs to the product
      variant = await ProductVariant.findOne({
        _id: variantId,
        product: productId
      });

      if (!variant) {
        throwBadRequestError(messages.VARIANT_NOT_FOUND);
      }
    }

    // Check variant stock
    if (quantity > variant.stock) {
      throwBadRequestError(messages.INSUFFICIENT_STOCK);
    }

    // Convert Map to object for storage
    variantAttributes = {};
    variant.attributes.forEach((value, key) => {
      variantAttributes[key] = value;
    });
  } else {
    // Regular product without variants
    // Check if quantity is valid
    if (quantity > product.stock) {
      throwBadRequestError(messages.INSUFFICIENT_STOCK);
    }
  }

  // Get cart or create if it doesn't exist
  let cart = await Cart.findOne({ user: userId });

  if (!cart) {
    cart = await Cart.create({
      user: userId,
      items: [],
      subtotal: 0
    });
  }

  // Determine the price to use
  let price;

  if (variant) {
    price = variant.discountPrice || variant.price;
  } else {
    price = product.discountPrice || product.price;
  }

  // Check if product/variant already in cart
  const itemIndex = cart.items.findIndex(item => {
    const productMatch = item.product.toString() === productId;

    // If no variant, just check product
    if (!variant) {
      return productMatch && !item.variant;
    }

    // If variant, check both product and variant
    return productMatch &&
           item.variant &&
           item.variant.toString() === variant._id.toString();
  });

  if (itemIndex > -1) {
    // Product/variant exists in cart, update quantity
    const newQuantity = cart.items[itemIndex].quantity + quantity;

    // Check stock based on whether we're using a variant or not
    if (variant) {
      if (newQuantity > variant.stock) {
        throwBadRequestError(messages.INSUFFICIENT_STOCK);
      }
    } else {
      if (newQuantity > product.stock) {
        throwBadRequestError(messages.INSUFFICIENT_STOCK);
      }
    }

    cart.items[itemIndex].quantity = newQuantity;
  } else {
    // Product/variant not in cart, add new item
    const newItem = {
      product: productId,
      quantity,
      price
    };

    // Add variant info if applicable
    if (variant) {
      newItem.variant = variant._id;
      newItem.variantAttributes = variantAttributes;
    }

    cart.items.push(newItem);
  }

  // Calculate subtotal based on stored prices
  cart.subtotal = cart.items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  // Update last active timestamp
  cart.lastActive = new Date();

  await cart.save();

  // Return populated cart
  const populatedCart = await Cart.findById(cart._id)
    .populate({
      path: 'items.product',
      select: 'name price discountPrice images stock status hasVariants'
    })
    .populate({
      path: 'items.variant',
      select: 'price discountPrice stock images attributes'
    });

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  const localizedPopulatedCart = await transformTranslatedFields(populatedCart.toObject({ getters: true }), language.code);

  return localizedPopulatedCart;
};

/**
 * Update cart item quantity
 */
const updateCartItem = async ({ userId, productId, variantId, quantity }) => {
  // Check if product exists and is active
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findOne({
    _id: productId,
    status: productStatusValue.ACTIVE
  });

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product has variants and variant ID is provided
  let variant = null;

  if (product.hasVariants && variantId) {
    // Validate variant ID
    if (!mongoose.Types.ObjectId.isValid(variantId)) {
      throwBadRequestError(messages.INVALID_VARIANT_ID);
    }

    // Check if variant exists and belongs to the product
    variant = await ProductVariant.findOne({
      _id: variantId,
      product: productId
    });

    if (!variant) {
      throwBadRequestError(messages.VARIANT_NOT_FOUND);
    }

    // Check variant stock
    if (quantity > variant.stock) {
      throwBadRequestError(messages.INSUFFICIENT_STOCK);
    }
  } else {
    // Regular product without variants
    // Check if quantity is valid
    if (quantity > product.stock) {
      throwBadRequestError(messages.INSUFFICIENT_STOCK);
    }
  }

  // Get cart
  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    throwBadRequestError(messages.CART_NOT_FOUND);
  }

  // Check if product/variant in cart
  const itemIndex = cart.items.findIndex(item => {
    const productMatch = item.product.toString() === productId;

    // If no variant, just check product
    if (!variant) {
      return productMatch && !item.variant;
    }

    // If variant, check both product and variant
    return productMatch &&
           item.variant &&
           item.variant.toString() === variant._id.toString();
  });

  if (itemIndex === -1) {
    throwBadRequestError(messages.ITEM_NOT_IN_CART);
  }

  // Determine the price to use
  let price;

  if (variant) {
    price = variant.discountPrice || variant.price;
  } else {
    price = product.discountPrice || product.price;
  }

  // Update quantity and price
  cart.items[itemIndex].quantity = quantity;
  cart.items[itemIndex].price = price;

  // Calculate subtotal based on stored prices
  cart.subtotal = cart.items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  // Update last active timestamp
  cart.lastActive = new Date();

  await cart.save();

  // Return populated cart
  const populatedCart = await Cart.findById(cart._id)
    .populate({
      path: 'items.product',
      select: 'name price discountPrice images stock status hasVariants'
    })
    .populate({
      path: 'items.variant',
      select: 'price discountPrice stock images attributes'
    });

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  const localizedPopulatedCart = await transformTranslatedFields(populatedCart.toObject({ getters: true }), language.code);

  return localizedPopulatedCart;
};

/**
 * Remove item from cart
 */
const removeFromCart = async ({ userId, productId, variantId }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // Get cart
  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    throwBadRequestError(messages.CART_NOT_FOUND);
  }

  // Check if product/variant in cart
  const itemIndex = cart.items.findIndex(item => {
    const productMatch = item.product.toString() === productId;

    // If no variant ID provided, remove the first matching product
    if (!variantId) {
      return productMatch;
    }

    // If variant ID provided, check both product and variant
    return productMatch &&
           item.variant &&
           item.variant.toString() === variantId;
  });

  if (itemIndex === -1) {
    throwBadRequestError(messages.ITEM_NOT_IN_CART);
  }

  // Remove item
  cart.items.splice(itemIndex, 1);

  // Calculate subtotal based on stored prices
  cart.subtotal = cart.items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  // Update last active timestamp
  cart.lastActive = new Date();

  await cart.save();

  // Return populated cart
  const populatedCart = await Cart.findById(cart._id)
    .populate({
      path: 'items.product',
      select: 'name price discountPrice images stock status hasVariants'
    })
    .populate({
      path: 'items.variant',
      select: 'price discountPrice stock images attributes'
    });

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  const localizedPopulatedCart = await transformTranslatedFields(populatedCart.toObject({ getters: true }), language.code);

  return localizedPopulatedCart;
};

/**
 * Clear cart
 */
const clearCart = async (userId) => {
  // Get cart
  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    throwBadRequestError(messages.CART_NOT_FOUND);
  }

  // Clear items and reset subtotal
  cart.items = [];
  cart.subtotal = 0;

  // Update last active timestamp
  cart.lastActive = new Date();

  await cart.save();

  return cart;
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart
};
