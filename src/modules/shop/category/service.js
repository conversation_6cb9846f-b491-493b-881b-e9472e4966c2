const Category = require('../../../models/Category');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { transformTranslatedFields } = require('../../../utils/localizer');
const mongoose = require('mongoose');
const User = require('../../../models/User');
const Language = require('../../../models/Language');

/**
 * Get all active categories
 */
const getAllCategories = async ({ page, limit, sortBy, sortOrder, search }, user = null) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const language = await Language.findOne({ name: user.preferredLanguage });

  const matchStage = { isActive: true };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }
  const categories = await Category.find(matchStage)
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  // Localize categories if user is provided
  return await transformTranslatedFields(categories, language.code);
};

/**
 * Get category by ID
 */
const getCategoryById = async (categoryId) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const language = { code: 'en' };

  const category = await Category.findOne({
    _id: categoryId,
    isActive: true
  }).lean();

  if (!category) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  return await transformTranslatedFields(category, language.code);
};

module.exports = {
  getAllCategories,
  getCategoryById
};
