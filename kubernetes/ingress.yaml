apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: ek-ishwar
  name: ingress-ek-ishwar
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: service-ek-ishwar
                port:
                  number: 80